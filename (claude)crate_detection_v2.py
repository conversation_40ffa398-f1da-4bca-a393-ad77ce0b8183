import cv2
import numpy as np
from collections import Counter, deque
import time
from sklearn.cluster import KMeans
import warnings
warnings.filterwarnings('ignore')

class AdvancedPouchColorDetector:
    def __init__(self):
        # SKU color definitions (NO WHITE)
        self.sku_colors = {
            'GREEN': {
                'hsv_range': [(40, 40, 40), (80, 255, 255)],
                'rgb_signature': [50, 150, 50],
                'display_color': (0, 255, 0)
            },
            'PINK': {
                'hsv_range': [(140, 50, 50), (170, 255, 255)],
                'rgb_signature': [200, 100, 150],
                'display_color': (255, 0, 255)
            },
            'YELLOW': {
                'hsv_range': [(20, 100, 100), (35, 255, 255)],
                'rgb_signature': [200, 200, 50],
                'display_color': (0, 255, 255)
            },
            'BLUE': {
                'hsv_range': [(85, 50, 50), (105, 255, 255)],
                'rgb_signature': [50, 100, 200],
                'display_color': (255, 100, 0)
            }
        }
        
        # Tray blue to filter out (different from product blue)
        self.tray_hsv_range = [(106, 100, 20), (130, 255, 150)]
        
        # Strategy states - Optimized: Start with only fast strategies enabled
        self.strategies = {
            '1_HSV_RANGE': True,        # Fast - keep enabled
            '2_KMEANS_CLUSTER': False,  # Slow - disable by default  
            '3_EDGE_COLOR': False,      # Medium - disable by default
            '4_HISTOGRAM_PEAK': False,  # Medium - disable by default
            '5_SIGNATURE_MATCH': True   # Fast - keep enabled
        }
        
        # Detection parameters
        self.debug_mode = False
        self.detection_history = deque(maxlen=30)
        self.strategy_results = {}
        self.confidence_threshold = 15.0
        
        # Performance optimizations
        self.frame_skip = 1  # Process every frame by default
        self.frame_counter = 0
        self.cached_tray_mask = None
        self.mask_cache_frame = None
        self.last_result = None  # Cache last result for skipped frames
        
        # Debug windows
        self.debug_windows = []
        
    def create_tray_mask(self, hsv_image):
        """Create mask to exclude blue tray areas with caching for performance"""
        # Simple cache based on frame shape - improves performance significantly
        if (self.cached_tray_mask is None or 
            self.mask_cache_frame is None or 
            hsv_image.shape != self.mask_cache_frame):
            
            tray_mask = cv2.inRange(hsv_image, self.tray_hsv_range[0], self.tray_hsv_range[1])
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))  # Smaller kernel for speed
            tray_mask = cv2.dilate(tray_mask, kernel, iterations=1)  # Fewer iterations
            self.cached_tray_mask = cv2.bitwise_not(tray_mask)
            self.mask_cache_frame = hsv_image.shape
            
        return self.cached_tray_mask
    
    def strategy_1_hsv_range_detection(self, image, hsv, mask):
        """Strategy 1: Optimized HSV color range detection"""
        if not self.strategies['1_HSV_RANGE']:
            return None, 0, None
        
        results = {}
        best_color = None
        best_score = 0
        debug_img = np.zeros_like(image) if self.debug_mode else None
        
        for color_name, color_info in self.sku_colors.items():
            # Create color mask
            color_mask = cv2.inRange(hsv, color_info['hsv_range'][0], color_info['hsv_range'][1])
            color_mask = cv2.bitwise_and(color_mask, mask)
            
            # Optimized morphological operations - smaller kernel, fewer operations
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))  # Smaller kernel
            color_mask = cv2.morphologyEx(color_mask, cv2.MORPH_CLOSE, kernel)
            
            # Fast scoring using pixel count instead of contour analysis
            pixel_count = cv2.countNonZero(color_mask)
            score = (pixel_count / (image.shape[0] * image.shape[1])) * 100
            results[color_name] = score
            
            if score > best_score:
                best_score = score
                best_color = color_name
            
            # Only compute contours for debug mode to save time
            if self.debug_mode and debug_img is not None:
                contours, _ = cv2.findContours(color_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if area > 500:  # Minimum area threshold
                        cv2.drawContours(debug_img, [contour], -1, color_info['display_color'], 2)
        
        return best_color if best_score > self.confidence_threshold else None, best_score, debug_img
        
        return best_color if best_score > self.confidence_threshold else None, best_score, debug_img
    
    def strategy_2_kmeans_clustering(self, image, mask):
        """Strategy 2: Optimized K-means clustering (expensive - use sparingly)"""
        if not self.strategies['2_KMEANS_CLUSTER']:
            return None, 0, None
        
        # Skip frames for expensive operations
        self.frame_counter += 1
        if self.frame_counter % 10 != 0:  # Only run every 10th frame
            return None, 0, None
        
        # Prepare data
        masked_pixels = image[mask > 0]
        if len(masked_pixels) < 100:
            return None, 0, None
        
        # Heavy subsampling for speed
        sample_size = min(1000, len(masked_pixels))  # Reduced from 5000
        indices = np.random.choice(len(masked_pixels), sample_size, replace=False)
        pixels = masked_pixels[indices].reshape(-1, 3)
        
        # K-means clustering with reduced parameters for speed
        kmeans = KMeans(n_clusters=3, random_state=42, n_init=3, max_iter=50)  # Reduced parameters
        kmeans.fit(pixels)
        
        # Get cluster centers and sizes
        labels = kmeans.labels_
        centers = kmeans.cluster_centers_
        unique, counts = np.unique(labels, return_counts=True)
        
        # Match clusters to SKU colors
        best_match = None
        best_score = 0
        debug_img = np.zeros_like(image) if self.debug_mode else None
        
        for i, center in enumerate(centers):
            cluster_size = counts[i] / len(labels)
            if cluster_size < 0.15:  # Skip small clusters
                continue
            
            # Find closest SKU color
            for color_name, color_info in self.sku_colors.items():
                rgb_sig = np.array(color_info['rgb_signature'])
                distance = np.linalg.norm(center - rgb_sig)
                
                if distance < 80:  # Tighter threshold for speed
                    score = cluster_size * 100 * (1 - distance/80)
                    if score > best_score:
                        best_score = score
                        best_match = color_name
                    
                    if self.debug_mode and debug_img is not None:
                        # Visualize cluster
                        debug_img[:50, i*50:(i+1)*50] = center.astype(np.uint8)
        
        return best_match if best_score > self.confidence_threshold else None, best_score, debug_img
    
    def strategy_3_edge_color_analysis(self, image, hsv, mask):
        """Strategy 3: Analyze colors along edges (where logos/text appear)"""
        if not self.strategies['3_EDGE_COLOR']:
            return None, 0, None
        
        # Find edges
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        edges = cv2.bitwise_and(edges, mask)
        
        # Dilate edges to get surrounding pixels
        kernel = np.ones((5, 5), np.uint8)
        edge_region = cv2.dilate(edges, kernel, iterations=2)
        
        # Analyze colors in edge regions
        edge_pixels = hsv[edge_region > 0]
        if len(edge_pixels) < 100:
            return None, 0, None
        
        # Check each SKU color
        best_match = None
        best_score = 0
        debug_img = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
        
        for color_name, color_info in self.sku_colors.items():
            # Count pixels in color range
            in_range = 0
            for pixel in edge_pixels:
                h, s, v = pixel
                lower = color_info['hsv_range'][0]
                upper = color_info['hsv_range'][1]
                
                if (lower[0] <= h <= upper[0] and 
                    lower[1] <= s <= upper[1] and 
                    lower[2] <= v <= upper[2]):
                    in_range += 1
            
            score = (in_range / len(edge_pixels)) * 100
            
            if score > best_score:
                best_score = score
                best_match = color_name
            
            if self.debug_mode and score > 5:
                # Highlight detected edge colors
                color_mask = cv2.inRange(hsv, color_info['hsv_range'][0], color_info['hsv_range'][1])
                edge_color = cv2.bitwise_and(edge_region, color_mask)
                debug_img[edge_color > 0] = color_info['display_color']
        
        return best_match if best_score > self.confidence_threshold else None, best_score, debug_img
    
    def strategy_4_histogram_peak_analysis(self, image, hsv, mask):
        """Strategy 4: Analyze histogram peaks in HSV channels"""
        if not self.strategies['4_HISTOGRAM_PEAK']:
            return None, 0, None
        
        # Check if mask has valid pixels
        if np.sum(mask) < 100:
            return None, 0, None
        
        # Calculate histograms using the original HSV image and mask
        h_hist = cv2.calcHist([hsv], [0], mask, [180], [0, 180])
        s_hist = cv2.calcHist([hsv], [1], mask, [256], [0, 256])
        
        # Smooth histograms
        h_hist = cv2.GaussianBlur(h_hist, (1, 5), 0)
        s_hist = cv2.GaussianBlur(s_hist, (1, 5), 0)
        
        # Find peaks
        h_peaks = []
        for i in range(1, len(h_hist)-1):
            if h_hist[i] > h_hist[i-1] and h_hist[i] > h_hist[i+1] and h_hist[i] > np.max(h_hist) * 0.1:
                h_peaks.append(i)
        
        # Match peaks to SKU colors
        best_match = None
        best_score = 0
        
        # Create debug visualization
        debug_img = np.ones((200, 360, 3), dtype=np.uint8) * 255
        
        for peak in h_peaks:
            peak_value = peak[0] if isinstance(peak, np.ndarray) else peak
            
            for color_name, color_info in self.sku_colors.items():
                h_range = color_info['hsv_range']
                if h_range[0][0] <= peak_value <= h_range[1][0]:
                    # Calculate score based on peak prominence and saturation
                    peak_strength = float(h_hist[peak])
                    score = (peak_strength / np.sum(h_hist)) * 100
                    
                    if score > best_score:
                        best_score = score
                        best_match = color_name
                    
                    if self.debug_mode:
                        # Draw histogram with peaks
                        for i in range(len(h_hist)):
                            height = int(h_hist[i] * 150 / np.max(h_hist))
                            cv2.line(debug_img, (i*2, 200), (i*2, 200-height), (0, 255, 0), 2)
                        cv2.circle(debug_img, (peak_value*2, 50), 5, color_info['display_color'], -1)
        
        return best_match if best_score > self.confidence_threshold else None, best_score, debug_img
    
    def strategy_5_signature_matching(self, image, mask):
        """Strategy 5: Color signature matching using statistical features"""
        if not self.strategies['5_SIGNATURE_MATCH']:
            return None, 0, None
        
        # Get masked pixels
        masked_pixels = image[mask > 0]
        if len(masked_pixels) < 100:
            return None, 0, None
        
        # Calculate color statistics
        mean_color = np.mean(masked_pixels, axis=0)
        std_color = np.std(masked_pixels, axis=0)
        
        # Calculate color moments
        moments = {
            'mean': mean_color,
            'std': std_color,
            'skew': np.array([np.mean(((masked_pixels[:, i] - mean_color[i]) / std_color[i])**3) 
                              if std_color[i] > 0 else 0 for i in range(3)])
        }
        
        # Match against SKU signatures
        best_match = None
        best_score = 0
        
        debug_img = np.ones((100, 300, 3), dtype=np.uint8) * 255
        
        for color_name, color_info in self.sku_colors.items():
            signature = np.array(color_info['rgb_signature'])
            
            # Calculate similarity score
            color_dist = np.linalg.norm(mean_color - signature)
            score = max(0, 100 - color_dist)
            
            # Boost score if standard deviation is low (uniform color)
            if np.mean(std_color) < 50:
                score *= 1.2
            
            if score > best_score:
                best_score = score
                best_match = color_name
            
            if self.debug_mode:
                # Visualize signatures
                y_pos = 20 + list(self.sku_colors.keys()).index(color_name) * 20
                cv2.putText(debug_img, f"{color_name}: {score:.1f}", (10, y_pos),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color_info['display_color'], 1)
                cv2.rectangle(debug_img, (150, y_pos-10), (150+int(score), y_pos),
                             color_info['display_color'], -1)
        
        return best_match if best_score > self.confidence_threshold else None, best_score, debug_img
    
    def highlight_color_regions_fast(self, image, detected_color):
        """Fast highlighting with minimal processing"""
        if detected_color not in self.sku_colors:
            return image
        
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        color_info = self.sku_colors[detected_color]
        
        # Create mask for detected color
        mask = cv2.inRange(hsv, color_info['hsv_range'][0], color_info['hsv_range'][1])
        
        # Simple morphological cleanup
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        
        # Fast contour detection with area filter
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Draw only significant contours
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 200:  # Lower threshold for faster detection
                # Draw contour outline
                cv2.drawContours(image, [contour], -1, color_info['display_color'], 2)
                
                # Add bounding box
                x, y, w, h = cv2.boundingRect(contour)
                cv2.rectangle(image, (x, y), (x+w, y+h), color_info['display_color'], 2)
        
        return image
    
    def highlight_color_regions_ultra_fast(self, image, detected_color):
        """Ultra-fast highlighting - simple indicator only"""
        if detected_color not in self.sku_colors:
            return image
        
        color_info = self.sku_colors[detected_color]
        
        # Just draw a simple rectangle indicator - no mask processing
        h, w = image.shape[:2]
        cv2.rectangle(image, (10, 10), (200, 60), color_info['display_color'], 3)
        cv2.putText(image, f"DETECTED: {detected_color}", (15, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, color_info['display_color'], 2)
        
        return image
    
    def process_frame_normal(self, frame):
        """Normal frame processing with all features but optimized"""
        
        # Resize frame for faster processing if it's too large
        height, width = frame.shape[:2]
        if width > 800:
            scale = 800 / width
            new_width = int(width * scale)
            new_height = int(height * scale)
            frame = cv2.resize(frame, (new_width, new_height))
        
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        tray_mask = self.create_tray_mask(hsv)
        
        # Run strategies based on current settings
        results = {}
        debug_images = {}
        
        # Strategy 1: HSV Range Detection (Fast - always run when enabled)
        if self.strategies['1_HSV_RANGE']:
            color, score, debug = self.strategy_1_hsv_range_detection(frame, hsv, tray_mask)
            if color:
                results['1_HSV_RANGE'] = (color, score)
                if debug is not None:
                    debug_images['1_HSV_RANGE'] = debug
        
        # Strategy 2: K-means Clustering (Expensive - run occasionally)
        if self.strategies['2_KMEANS_CLUSTER']:
            color, score, debug = self.strategy_2_kmeans_clustering(frame, tray_mask)
            if color:
                results['2_KMEANS_CLUSTER'] = (color, score)
                if debug is not None:
                    debug_images['2_KMEANS_CLUSTER'] = debug
        
        # Strategy 3: Edge Color Analysis (Only if explicitly enabled)
        if self.strategies['3_EDGE_COLOR']:
            color, score, debug = self.strategy_3_edge_color_analysis(frame, hsv, tray_mask)
            if color:
                results['3_EDGE_COLOR'] = (color, score)
                if debug is not None:
                    debug_images['3_EDGE_COLOR'] = debug
        
        # Strategy 4: Histogram Peak Analysis (Only if explicitly enabled)
        if self.strategies['4_HISTOGRAM_PEAK']:
            color, score, debug = self.strategy_4_histogram_peak_analysis(frame, hsv, tray_mask)
            if color:
                results['4_HISTOGRAM_PEAK'] = (color, score)
                if debug is not None:
                    debug_images['4_HISTOGRAM_PEAK'] = debug
        
        # Strategy 5: Signature Matching (Fast - run when enabled)
        if self.strategies['5_SIGNATURE_MATCH']:
            color, score, debug = self.strategy_5_signature_matching(frame, tray_mask)
            if color:
                results['5_SIGNATURE_MATCH'] = (color, score)
                if debug is not None:
                    debug_images['5_SIGNATURE_MATCH'] = debug
        
        self.strategy_results = results
        
        # Decision logic for normal mode
        if results:
            color_votes = Counter([color for color, _ in results.values()])
            if color_votes:
                final_color = color_votes.most_common(1)[0][0]
                vote_count = color_votes[final_color]
                
                # Need at least 1 vote for fast strategies, 2 for comprehensive
                min_votes = 1 if not (self.strategies['2_KMEANS_CLUSTER'] or self.strategies['3_EDGE_COLOR'] or self.strategies['4_HISTOGRAM_PEAK']) else 2
                
                if vote_count >= min_votes:
                    return final_color, results, debug_images
        
        return None, results, debug_images
    
    def process_frame_ultra_fast(self, frame):
        """Ultra-fast frame processing - absolute minimum operations"""
        self.frame_counter += 1
        
        # Skip more frames for maximum speed
        if self.frame_counter % 3 == 0:  # Process every 3rd frame only
            return self.last_result if self.last_result else (None, {})
        
        # Super aggressive resizing
        height, width = frame.shape[:2]
        if width > 320:  # Very small for maximum speed
            frame = cv2.resize(frame, (320, 240))
        
        # Only HSV conversion
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # Only fastest detection
        results = {}
        color, score = self.strategy_1_ultra_fast(hsv)
        if color:
            results['1_HSV_RANGE'] = (color, score)
            self.last_result = (color, results)
            return color, results
        
        self.last_result = (None, results)
        return None, results
    
    def strategy_1_ultra_fast(self, hsv):
        """Ultra-fast HSV detection - sample center pixels only"""
        # Use tiny center region
        h, w = hsv.shape[:2]
        center_h, center_w = h//3, w//3
        roi = hsv[center_h:2*center_h, center_w:2*center_w]  # Center 33% only
        
        # Further subsample the ROI for speed
        roi = roi[::2, ::2]  # Take every other pixel
        
        best_color = None
        best_score = 0
        
        for color_name, color_info in self.sku_colors.items():
            # Direct pixel counting without mask creation
            lower = np.array(color_info['hsv_range'][0])
            upper = np.array(color_info['hsv_range'][1])
            
            # Vectorized comparison (faster than cv2.inRange)
            mask = np.all((roi >= lower) & (roi <= upper), axis=2)
            pixel_count = np.sum(mask)
            
            if pixel_count > best_score and pixel_count > 10:  # Very low threshold
                best_score = pixel_count
                best_color = color_name
        
        return best_color, best_score
    
    def create_visualization_enhanced(self, frame, final_color, results, mode="ultra_fast"):
        """Enhanced visualization with all features but optimized"""
        viz = frame.copy()
        height, width = viz.shape[:2]
        
        # Highlight detected regions based on mode
        if final_color:
            if mode == "ultra_fast":
                viz = self.highlight_color_regions_ultra_fast(viz, final_color)
            else:
                viz = self.highlight_color_regions_fast(viz, final_color)
        
        # Create comprehensive info panel
        panel_height = 160 if mode == "ultra_fast" else 200
        info_panel = np.ones((panel_height, width, 3), dtype=np.uint8) * 40
        
        # Title
        title = "ULTRA-FAST MODE" if mode == "ultra_fast" else "OPTIMIZED MODE"
        cv2.putText(info_panel, f"POUCH DETECTION - {title}", (10, 25), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        
        # Strategy status (simplified for ultra-fast, detailed for normal)
        if mode == "ultra_fast":
            cv2.putText(info_panel, "Strategy: Ultra-Fast HSV Detection Only", (10, 50),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        else:
            y_offset = 50
            for strategy_name, enabled in self.strategies.items():
                status = "ON" if enabled else "OFF"
                status_color = (0, 255, 0) if enabled else (0, 0, 255)
                
                text = f"{strategy_name}: {status}"
                if enabled and strategy_name in results:
                    color, score = results[strategy_name]
                    text += f" -> {color} ({score:.1f}%)"
                    cv2.putText(info_panel, text, (10, y_offset), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, self.sku_colors[color]['display_color'], 1)
                else:
                    cv2.putText(info_panel, text, (10, y_offset), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, status_color, 1)
                y_offset += 20
        
        # Final result box
        result_y = 80 if mode == "ultra_fast" else 130
        cv2.rectangle(info_panel, (width//2 - 150, result_y-20), (width//2 + 150, result_y+30), (255, 255, 255), 2)
        if final_color:
            cv2.putText(info_panel, f"DETECTED: {final_color}", (width//2 - 130, result_y+5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, self.sku_colors[final_color]['display_color'], 2)
        else:
            cv2.putText(info_panel, "NO DETECTION", (width//2 - 80, result_y+5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
        
        # Controls based on mode
        controls_y = panel_height - 40
        if mode == "ultra_fast":
            controls = [
                "CONTROLS: [u] Normal Mode | [q] Quit | [p] Pause | [s] Save",
                "Ultra-Fast: Max speed, center detection only"
            ]
        else:
            controls = [
                "CONTROLS: [u] Ultra Mode | [1-5] Toggle Strategies | [d] Debug | [q] Quit | [p] Pause",
                f"Debug: {'ON' if self.debug_mode else 'OFF'} | Active: {sum(self.strategies.values())}/5 | [f] Fast Mode"
            ]
        
        for i, control in enumerate(controls):
            cv2.putText(info_panel, control, (10, controls_y + i*15),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.35, (200, 200, 200), 1)
        
        # Combine frame and panel
        combined = np.vstack([viz, info_panel])
        
        return combined
    
    def show_debug_windows(self, debug_images):
        """Display debug windows for active strategies"""
        if not self.debug_mode or not debug_images:
            return
        
        for strategy_name, debug_img in debug_images.items():
            if debug_img is not None and self.strategies[strategy_name]:
                window_name = f"Debug: {strategy_name}"
                cv2.imshow(window_name, debug_img)
                if window_name not in self.debug_windows:
                    self.debug_windows.append(window_name)
    
    def close_debug_windows(self):
        """Close all debug windows"""
        for window in self.debug_windows:
            cv2.destroyWindow(window)
        self.debug_windows = []
    
    def toggle_strategy(self, key):
        """Toggle strategy on/off based on key press"""
        strategy_map = {
            ord('1'): '1_HSV_RANGE',
            ord('2'): '2_KMEANS_CLUSTER',
            ord('3'): '3_EDGE_COLOR',
            ord('4'): '4_HISTOGRAM_PEAK',
            ord('5'): '5_SIGNATURE_MATCH'
        }
        
        if key in strategy_map:
            strategy = strategy_map[key]
            self.strategies[strategy] = not self.strategies[strategy]
            print(f"Strategy {strategy}: {'ENABLED' if self.strategies[strategy] else 'DISABLED'}")
            return True
        return False

def main():
    """Main application loop"""
    import sys
    
    print("="*60)
    print("OPTIMIZED POUCH COLOR DETECTION SYSTEM V2.0")
    print("="*60)
    print("\nSKU COLORS: GREEN | PINK | YELLOW | BLUE")
    print("\nOPTIMIZED STRATEGIES (Fast by default):")
    print("1. HSV Range Detection - Fast color detection (ENABLED)")
    print("2. K-means Clustering - Expensive ML method (DISABLED)")
    print("3. Edge Color Analysis - Medium cost (DISABLED)")
    print("4. Histogram Peak Analysis - Medium cost (DISABLED)")
    print("5. Signature Matching - Fast color signature (ENABLED)")
    print("\nCONTROLS:")
    print("[1-5] Toggle individual strategies ON/OFF")
    print("[d]   Toggle debug mode (show detection process)")
    print("[p]   Pause/Resume")
    print("[s]   Save current frame")
    print("[f]   Toggle fast mode (disable slow strategies)")
    print("[q]   Quit")
    print("="*60)
    
    # Check for command-line arguments
    if len(sys.argv) > 1:
        source = sys.argv[1]
        print(f"\nUsing video source from command line: {source}")
        if source == '0':
            source = 0
    else:
        # Ask for input source
        source = input("\nEnter video path or '0' for webcam (press Enter for 'video.mp4'): ").strip()
        if source == '':
            source = 'video.mp4'
        elif source == '0':
            source = 0
    
    # Initialize detector
    detector = AdvancedPouchColorDetector()
    
    # Open video capture
    cap = cv2.VideoCapture(source)
    if not cap.isOpened():
        # Try as image
        image = cv2.imread(str(source))
        if image is not None:
            print("\nProcessing as image...")
            while True:
                final_color, results, debug_images = detector.process_frame(image)
                viz = detector.create_visualization(image, final_color, results)
                
                cv2.imshow('Optimized Pouch Detection', viz)
                detector.show_debug_windows(debug_images)
                
                key = cv2.waitKey(1) & 0xFF
                
                if key == ord('q'):
                    break
                elif key == ord('d'):
                    detector.debug_mode = not detector.debug_mode
                    if not detector.debug_mode:
                        detector.close_debug_windows()
                    print(f"Debug mode: {'ON' if detector.debug_mode else 'OFF'}")
                elif key == ord('s'):
                    filename = f"detection_{int(time.time())}.jpg"
                    cv2.imwrite(filename, viz)
                    print(f"Saved: {filename}")
                elif key == ord('f'):
                    # Toggle fast mode
                    fast_mode = not detector.strategies.get('2_KMEANS_CLUSTER', False)
                    if fast_mode:
                        detector.strategies['2_KMEANS_CLUSTER'] = False
                        detector.strategies['3_EDGE_COLOR'] = False
                        detector.strategies['4_HISTOGRAM_PEAK'] = False
                        print("FAST MODE: Only HSV and Signature strategies enabled")
                    else:
                        detector.strategies['2_KMEANS_CLUSTER'] = True
                        detector.strategies['3_EDGE_COLOR'] = True
                        detector.strategies['4_HISTOGRAM_PEAK'] = True
                        print("FULL MODE: All strategies enabled (slower)")
                elif detector.toggle_strategy(key):
                    pass  # Strategy toggled
        else:
            print(f"Error: Could not open {source}")
            return
    else:
        # Set capture properties for maximum speed
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 320)   # Much smaller resolution
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 240)
        cap.set(cv2.CAP_PROP_FPS, 15)            # Lower FPS for stability
        
        # Video processing
        paused = False
        frame_count = 0
        fps_start = time.time()
        fps = 0
        ultra_fast_mode = True  # Start in ultra-fast mode
        
        print("\nProcessing video in ULTRA-FAST mode... Maximum speed!")
        print("Press 'u' to toggle ultra-fast mode, 'q' to quit")
        
        while True:
            if not paused:
                ret, frame = cap.read()
                if not ret:
                    if source != 0:  # Loop video
                        cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                        continue
                    else:
                        break
                
                # Choose processing method based on mode
                if ultra_fast_mode:
                    final_color, results = detector.process_frame_ultra_fast(frame)
                    viz = detector.create_visualization_enhanced(frame, final_color, results, "ultra_fast")
                    debug_images = {}
                else:
                    final_color, results, debug_images = detector.process_frame_normal(frame)
                    viz = detector.create_visualization_enhanced(frame, final_color, results, "normal")
                    detector.show_debug_windows(debug_images)
                
                # Add FPS counter
                frame_count += 1
                if frame_count % 30 == 0:
                    fps = frame_count / (time.time() - fps_start)
                    frame_count = 0
                    fps_start = time.time()
                
                cv2.putText(viz, f"FPS: {fps:.1f}", (viz.shape[1] - 100, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                
                # Show mode
                mode_text = "ULTRA-FAST" if ultra_fast_mode else "NORMAL"
                cv2.putText(viz, f"Mode: {mode_text}", (viz.shape[1] - 150, 60),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
                
                # Update detection history
                if final_color:
                    detector.detection_history.append(final_color)
                
                # Show frames
                window_name = 'Ultra-Fast Pouch Detection' if ultra_fast_mode else 'Optimized Pouch Detection'
                cv2.imshow(window_name, viz)
            else:
                # Show pause indicator
                if 'viz' in locals():
                    paused_viz = viz.copy()
                    cv2.putText(paused_viz, "PAUSED", (viz.shape[1]//2 - 50, viz.shape[0]//2),
                               cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 255), 3)
                    window_name = 'Ultra-Fast Pouch Detection' if ultra_fast_mode else 'Optimized Pouch Detection'
                    cv2.imshow(window_name, paused_viz)
            
            # Handle key press
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('q'):
                break
            elif key == ord('p'):
                paused = not paused
                print(f"{'PAUSED' if paused else 'RESUMED'}")
            elif key == ord('u'):
                # Toggle ultra-fast mode
                ultra_fast_mode = not ultra_fast_mode
                if ultra_fast_mode:
                    print("ULTRA-FAST MODE: Maximum speed, minimal processing")
                    detector.close_debug_windows()
                else:
                    print("NORMAL MODE: Full processing (slower)")
            elif key == ord('d') and not ultra_fast_mode:
                detector.debug_mode = not detector.debug_mode
                if not detector.debug_mode:
                    detector.close_debug_windows()
                print(f"Debug mode: {'ON' if detector.debug_mode else 'OFF'}")
            elif key == ord('s'):
                filename = f"detection_{int(time.time())}.jpg"
                cv2.imwrite(filename, viz)
                print(f"Saved: {filename}")
            elif key == ord('f') and not ultra_fast_mode:
                # Toggle fast mode (only in normal mode)
                fast_mode = not detector.strategies.get('2_KMEANS_CLUSTER', False)
                if fast_mode:
                    detector.strategies['2_KMEANS_CLUSTER'] = False
                    detector.strategies['3_EDGE_COLOR'] = False
                    detector.strategies['4_HISTOGRAM_PEAK'] = False
                    print("FAST MODE: Only HSV and Signature strategies enabled")
                else:
                    detector.strategies['2_KMEANS_CLUSTER'] = True
                    detector.strategies['3_EDGE_COLOR'] = True
                    detector.strategies['4_HISTOGRAM_PEAK'] = True
                    print("FULL MODE: All strategies enabled (slower)")
            elif detector.toggle_strategy(key) and not ultra_fast_mode:
                pass  # Strategy toggled
    
    # Cleanup
    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    # Check dependencies
    try:
        from sklearn.cluster import KMeans
        main()
    except ImportError:
        print("\nPlease install required dependencies:")
        print("pip install opencv-python numpy scikit-learn")
        print("\nThen run the program again.")