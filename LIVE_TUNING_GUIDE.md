# Live Tuning System Guide

## Overview

The live tuning system provides real-time parameter adjustment through trackbars while the video is running. This allows for immediate feedback and fine-tuning without restarting the application.

## Activation

The live tuner is **automatically enabled** for video and webcam processing:

```bash
# Live tuner enabled (video/webcam)
python v1_optimized.py --input videos/4.mp4
python v1_optimized.py  # webcam

# Live tuner disabled (single image)
python v1_optimized.py --input Images/IMG_2467.jpg
```

## Tuning Interface

### Trackbar Window: "Live Tuner"
- **Resizable window** with all tuning parameters
- **Real-time updates** - changes apply immediately
- **Organized sections** for different parameter groups

### Hotkeys
- **'p'** - Print current parameter values to console
- **'z'** - Reset all parameters to defaults
- **'q'** - Quit application

## Parameter Categories

### A) Tray Detection Parameters

#### `tray_min` / `tray_max` (80-600 pixels)
- **Purpose**: Expected pixel distance between tray rails
- **Rule**: Set around average tray height ± 15-20%
- **Too low**: Noise detected as trays
- **Too high**: Real trays missed

#### `solid_frac x100` (20-70%)
- **Purpose**: Minimum solid blue fraction for tray detection
- **Rule**: Lower if trays look patchy/dirty; raise if false bands appear
- **Too low**: False positives from partial blue areas
- **Too high**: Real trays with gaps/dirt missed

#### `gap_blue_max x100` (10-40%)
- **Purpose**: Maximum blue allowed in gaps between trays
- **Rule**: Lower if trays merge; raise if real trays rejected
- **Too low**: Real trays with blue gaps rejected
- **Too high**: Separate trays merge into one

#### `band_frac x100` (20-95%)
- **Purpose**: Width of central detection band
- **Rule**: Widen if trays drift left/right; narrow for speed
- **Too narrow**: Trays outside detection zone missed
- **Too wide**: Side clutter causes false detections

### B) White-Based Pouch Counting

#### `LAB_L_MIN` (100-200)
- **Purpose**: Minimum lightness for white detection in LAB space
- **Rule**: Lower if shaded pouches disappear; raise if bright non-pouches enter
- **Too low**: Background noise counted as pouches
- **Too high**: Shaded white pouches missed

#### `LAB_AB_TOL` (10-40)
- **Purpose**: Tolerance for neutral colors (a/b near 128)
- **Rule**: Raise under warm/yellowish lighting; lower to fight tinted noise
- **Too low**: Slightly tinted whites missed
- **Too high**: Colored areas counted as white

#### `DT_PEAK_REL x100` (25-55%)
- **Purpose**: Distance transform peak threshold for watershed splitting
- **Rule**: Lower to split merged pouches; raise to avoid over-splitting
- **Too low**: Single pouches split into multiple
- **Too high**: Touching pouches not separated

#### `WHITE_OPEN_K` / `WHITE_CLOSE_K` (3-9, odd numbers)
- **Purpose**: Morphological operations for noise removal and gap filling
- **Rule**: Increase close to bridge gaps; increase open to kill speckles
- **Open too small**: Noise remains
- **Close too small**: Pouch cores fragmented

#### `TRAY_RIM_MARGIN` (5-40 pixels)
- **Purpose**: Pixels to ignore near tray edges
- **Rule**: Reduce to keep edge pouches; raise if rim glare becomes "white"
- **Too small**: Tray plastic counted as pouches
- **Too large**: Edge pouches missed

### C) Color Detection & White Gate

#### `WHITE_S_MAX` (50-200)
- **Purpose**: Maximum saturation for HSV white detection
- **Rule**: Raise if white gate rejects cream/printed areas; lower if colored backgrounds leak
- **Too low**: Printed text on pouches missed
- **Too high**: Colored backgrounds included

#### `WHITE_V_MIN` (100-220)
- **Purpose**: Minimum brightness for HSV white detection
- **Rule**: Lower if shadows block detection; raise if dull backgrounds leak
- **Too low**: Dark areas counted as white
- **Too high**: Shaded pouches missed

#### `GATE_EXPAND x1000` (5-50, represents 0.005-0.050)
- **Purpose**: Expansion factor for white gate around colored print
- **Rule**: Raise to better cover stripes off-white; lower to avoid tray contamination
- **Too small**: Colored text not detected
- **Too large**: Tray walls included in detection

## Tuning Workflow

### 1. Start with Defaults
```bash
python v1_optimized.py --input videos/4.mp4
```

### 2. Identify Issues
- **Tray detection**: Missing trays, false positives, merged trays
- **Pouch counting**: Under/over counting, poor separation
- **Color detection**: Missing colored areas, false colors

### 3. Adjust Parameters
- **One category at a time** - don't change everything at once
- **Small increments** - make gradual adjustments
- **Observe immediately** - changes apply in real-time

### 4. Save Tuned Values
- Press **'p'** to print current values
- Copy printed values to `config.py`
- Restart application to use new defaults

### 5. Reset if Needed
- Press **'z'** to reset all parameters to defaults
- Start tuning process over

## Common Tuning Scenarios

### Scenario 1: Missing Trays
**Symptoms**: Real trays not detected
**Solutions**:
- Lower `tray_min` / raise `tray_max`
- Lower `solid_frac x100`
- Raise `gap_blue_max x100`

### Scenario 2: False Tray Detections
**Symptoms**: Non-tray areas detected as trays
**Solutions**:
- Raise `tray_min` / lower `tray_max`
- Raise `solid_frac x100`
- Lower `gap_blue_max x100`

### Scenario 3: Under-counting Pouches
**Symptoms**: White-based count too low
**Solutions**:
- Lower `LAB_L_MIN`
- Raise `LAB_AB_TOL`
- Lower `DT_PEAK_REL x100`
- Raise `WHITE_CLOSE_K`

### Scenario 4: Over-counting Pouches
**Symptoms**: White-based count too high
**Solutions**:
- Raise `LAB_L_MIN`
- Lower `LAB_AB_TOL`
- Raise `DT_PEAK_REL x100`
- Lower `TRAY_RIM_MARGIN`

### Scenario 5: Poor Color Detection
**Symptoms**: Colored areas not detected
**Solutions**:
- Raise `WHITE_S_MAX`
- Lower `WHITE_V_MIN`
- Raise `GATE_EXPAND x1000`

## Best Practices

### 1. Systematic Approach
- Test one parameter at a time
- Document what works for different conditions
- Keep notes on optimal ranges

### 2. Lighting Considerations
- **Bright lighting**: Raise `LAB_L_MIN`, lower `WHITE_V_MIN`
- **Dim lighting**: Lower `LAB_L_MIN`, raise `WHITE_V_MIN`
- **Warm lighting**: Raise `LAB_AB_TOL`

### 3. Tray Variations
- **Clean trays**: Raise `solid_frac x100`
- **Dirty trays**: Lower `solid_frac x100`
- **Tight spacing**: Lower `gap_blue_max x100`
- **Loose spacing**: Raise `gap_blue_max x100`

### 4. Performance vs Accuracy
- **Higher accuracy**: Lower thresholds, more sensitive detection
- **Better performance**: Higher thresholds, less processing
- **Balance**: Find sweet spot for your specific use case

## Saving Configuration

After tuning, press **'p'** to get output like:
```
🎯 CURRENT TUNED PARAMETERS
============================================================
# Tray Detection
TRAY_MIN_HEIGHT_DEFAULT = 180
TRAY_MAX_HEIGHT_DEFAULT = 250
SOLID_FRACTION_DEFAULT = 0.400
GAP_BLUE_MAX_DEFAULT = 0.250
BAND_FRACTION_DEFAULT = 0.650

# White-based Pouch Counting
LAB_L_MIN = 130
LAB_AB_TOL = 25
DT_PEAK_REL = 0.400
WHITE_OPEN_K = 3
WHITE_CLOSE_K = 5
TRAY_RIM_MARGIN = 15

# White Gate / Color Detection
WHITE_S_MAX = 120
WHITE_V_MIN = 150
WHITE_GATE_EXPANSION_FACTOR = 0.0200
============================================================
```

Copy these values to `config.py` to make them permanent.

## Troubleshooting

### Tuner Not Appearing
- Only enabled for video/webcam, not single images
- Check console for "🎛️ LIVE TUNER CREATED" message

### Parameters Not Updating
- Ensure you're adjusting trackbars in "Live Tuner" window
- Changes apply immediately during video processing

### Values Reset on Restart
- Use **'p'** to print values before quitting
- Update `config.py` with printed values
- Tuner uses config.py defaults on startup

This live tuning system enables rapid optimization for different lighting conditions, tray types, and pouch varieties without code changes or application restarts.
