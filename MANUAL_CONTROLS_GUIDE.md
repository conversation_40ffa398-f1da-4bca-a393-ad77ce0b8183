# Enhanced Manual Controls Guide

## New Features Added to v1_optimized.py

### 🎮 **Manual Controls**

| Key | Function | Description |
|-----|----------|-------------|
| `i` | **Increase FPS** | Increases target FPS by 2 (range: 1-60) |
| `o` | **Decrease FPS** | Decreases target FPS by 2 (range: 1-60) |
| `SPACE` | **Pause/Resume** | Toggle video playback pause |
| `d` | **Debug Modes** | Cycle through 5 debug visualization modes |
| `s` | **Save Frame** | Save current frame as PNG |
| `q` or `ESC` | **Quit** | Exit the application |

### 🔍 **Debug Modes (Press 'd' to cycle)**

| Mode | Name | Description |
|------|------|-------------|
| **0** | LEN+GAP | Main detection view with small mask overlay |
| **1** | LEN+GAP+Mask | Main detection with full side-by-side mask |
| **2** | Prev+Lines | Previous logic with Hough lines + small mask |
| **3** | Prev+Mask | Previous logic with full side-by-side mask |
| **4** | Split | Split-screen comparison view |

### 📊 **On-Screen Display**
- **Current FPS**: Shows your manually set FPS target
- **Play Status**: PLAYING or PAUSED indicator
- **Debug Mode**: Shows current mode name
- **Tray Count**: Number of detected trays
- **Gate Info**: Detection gate status and total count
- **Controls**: Quick reference at bottom of screen

### 🚀 **Usage Examples**

#### Start with Slow, Controllable FPS:
```bash
python v1_optimized.py --input videos/mini.mp4 --target_fps 3
```

#### Real-time with Manual Control:
```bash
python v1_optimized.py --input videos/mini.mp4 --target_fps 15 --skip_frames 1
```

#### High Performance Testing:
```bash
python v1_optimized.py --input videos/mini.mp4 --target_fps 30 --skip_frames 1
```

### 💡 **Tips for Usage**

1. **Start Slow**: Begin with `--target_fps 3` to examine detection closely
2. **Use Pause**: Press `SPACE` to pause and examine specific frames
3. **Debug Modes**: Use `d` to see different detection methods:
   - Mode 0/1: See LEN+GAP detection (green lines)
   - Mode 2/3: See previous logic with Hough lines (yellow lines)
   - Mode 4: Compare both methods side-by-side
4. **FPS Control**: 
   - Press `i` to speed up when comfortable
   - Press `o` to slow down for detailed analysis
5. **Save Interesting Frames**: Press `s` to save frames showing good/bad detection

### 🔧 **Performance Notes**

- **FPS Range**: 1-60 FPS (software limited for stability)
- **Real-time Processing**: Actual processing can be much higher than display FPS
- **Pause Efficiency**: Pausing stops processing but keeps display active
- **Debug Impact**: Modes 2-3 are slightly slower due to additional computations

### 🎯 **Recommended Workflow**

1. **Initial Analysis**: Start at 3-5 FPS with debug mode 0
2. **Compare Methods**: Switch to mode 4 to see both detection methods
3. **Fine-tune**: Adjust FPS with `i`/`o` as needed
4. **Production**: Use 15-30 FPS for real-time applications

This enhanced version gives you complete control over the analysis speed and visualization, making it perfect for both detailed analysis and real-time processing!
