#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Awesome—here’s your field guide to every knob in config.py, what it means in plain English, and how turning it up/down changes behavior. Then I give you the top settings to tweak on site (separated by Tray counting vs. Pouch counting vs. Dominant color) plus ready-to-drop trackbar code so you can tune live.

What each setting means (and “higher vs lower”)
Color detection (HSV)

COLOR_RANGES
Which HSV ranges count as red/blue/green/yellow ink on pouches.
• Wider ranges = catch more shades but risk false hits.
• Narrow ranges = fewer false hits but may miss faint/dirty colors.

TRAY_BLUE_RANGE
HSV range for the blue plastic tray (used only to exclude rim/plastic).
• Wider range = better at excluding tray pixels; if too wide, it might suppress real pouch blues.
• Narrower range = might let tray plastic leak into detection.

WHITE_S_MAX (white gate: max saturation)
How “colored” a white pixel is allowed to be and still be treated as white.
• Higher = accepts cream/yellowish whites → more color detection on pouches; may let colored or reflective bits leak in.
• Lower = stricter “pure white” → safer but might miss off-white pouches in shadows.

WHITE_V_MIN (white gate: min brightness)
How bright a pixel must be to be considered white.
• Higher = only very bright pixels → robust but can miss shaded whites.
• Lower = accepts dimmer whites → better in shadows but may grab background noise.

WHITE_GATE_EXPANSION_FACTOR
How far to “grow” white areas to include colored text/stripes near the white.
• Higher = more generous halo → better at catching logos/stripes, but can bleed onto tray edges.
• Lower = tight halo → safer but may miss color that sits just off the white core.

White-based pouch counting

POUCH_COUNT_MODE ("white" | "color" | "auto")
Which method counts pouches.
• "white" = count using white cores (recommended).
• "color" = count colored blobs (can overcount logos).
• "auto" = try white first, fall back to color if white fails.

LAB_L_MIN (lightness threshold in LAB)
How bright something must be to be “white” in LAB.
• Higher = requires brighter whites → fewer false whites but misses shaded pouches.
• Lower = accepts darker whites → better shadow tolerance, can include non-pouch stuff.

LAB_AB_TOL (neutral color tolerance in LAB)
How close to gray a pixel must be to be “white” (distance from a=b=128).
• Higher = allows warm/cool whites (yellowish/blueish) → better coverage, a bit noisier.
• Lower = only neutral white → clean, but misses tinted plastics or warm lighting.

DT_PEAK_REL (watershed marker fraction)
Controls splitting of touching pouches via distance transform.
• Lower = more splits (aggressive) → good if pouches merge, may over-split noise.
• Higher = fewer splits (conservative) → stable but can undercount in stacks.

WHITE_OPEN_K / WHITE_CLOSE_K (morphology)
Clean/merge the white mask before splitting.
• Bigger kernels = remove specks & bridge gaps, but can eat detail or merge neighbors.
• Smaller kernels = preserve detail but keep noise.

Geometry & size filters

TRAY_RIM_MARGIN
Number of pixels around the tray ROI to ignore (to avoid rim).
• Higher = safer near edges; can drop edge pouches.
• Lower = recover edge pouches; may pick rim reflections.

ROI_MARGIN_DEFAULT
Padding added around detected tray bounds for cropping/analysis.
• Higher = more context; slightly slower and may include neighbors.
• Lower = tight crop; faster but can clip.

MIN_POUCH_AREA_FRACTION / MAX_POUCH_AREA_FRACTION
Allowed relative area for a pouch blob (fraction of tray ROI).
• Increase min = ignore tiny specks (logos/noise) but risk missing small pouches.
• Decrease min = capture small pouches but more noise.
• Decrease max = avoid merged blobs; increase max = tolerate bigger blobs.

MIN_POUCH_AREA_ABSOLUTE
Hard lower bound in pixels.
• Higher = extra filter on tiny noise; can miss small pouches on far cameras.
• Lower = more sensitive but noisier.

Image processing

CLAHE_CLIP_LIMIT / CLAHE_TILE_GRID_SIZE
Local contrast boost for edges.
• Higher clip = more contrast → stronger edges, may add grain.
• Smaller tiles = stronger local effect; bigger tiles = smoother.

COLOR_KERNEL_SIZE, MORPH_OPEN_ITERATIONS, MORPH_CLOSE_ITERATIONS
Cleanup for color masks.
• Bigger/More = smoother masks, fewer specks, risk of merging logos.
• Smaller/Fewer = preserve detail, more specks.

BAND_FRACTION_DEFAULT
How wide the central vertical band is for tray detection.
• Higher = wider band → more signal, more chance to catch trays, slightly slower.
• Lower = narrow band → faster, but miss if trays wander laterally.

SMOOTH_WINDOW_DEFAULT
Smoothing window for the “blue run” profile.
• Higher = smoother profile → stable lines, can blur short trays/gaps.
• Lower = more responsive but noisier.

Performance & gate

TARGET_FPS_DEFAULT / SKIP_FRAMES_DEFAULT
Display FPS and “process every Nth frame”.
• Higher FPS / smaller skip = smoother/accurate, heavier CPU.
• Lower FPS / bigger skip = lighter CPU, might miss transitions.

GATE_HEIGHT_DEFAULT, ENTER_FRAMES_DEFAULT, EXIT_FRAMES_DEFAULT
Region & debounce for counting trays.
• Taller gate / lower ENTER = quicker to count; may false-trigger.
• Shorter gate / higher ENTER = conservative; might miss fast trays.

Tray detection thresholds

TRAY_MIN_HEIGHT_DEFAULT / TRAY_MAX_HEIGHT_DEFAULT
Expected vertical distance (px) between the top and bottom tray rails.
• Raise min / lower max = stricter size → fewer false pairs, risk of missing actual trays if camera shifts/zoom changes.
• Lower min / raise max = more tolerant to scale changes.

SOLID_FRACTION_DEFAULT
How “solid” the blue band must be (fraction of band width that is blue).
• Higher = demands thicker continuous blue → fewer false bands; can drop faded/dirty trays.
• Lower = allows spottier blue → more sensitive; risk of mis-detection.

GAP_BLUE_MAX_DEFAULT
Max allowed blue inside the gap between two rails (gaps should be not blue).
• Lower = stricter gap (little blue allowed) → better separation, may fail when shadows make gap blue-ish.
• Higher = more tolerant; risk of merging two trays.

UI/Display & Colors

Self-explanatory; purely visual. Adjust for readability; they don’t affect detection.

The 5–10 knobs to tweak on site
A) Tray counting (separation & box fit)

TRAY_MIN_HEIGHT_DEFAULT / TRAY_MAX_HEIGHT_DEFAULT
Rule: set around the average pixel distance between the two rails; allow ±15–20%.

SOLID_FRACTION_DEFAULT
Rule: if trays look “patchy” or dirty, lower slightly; if false bands appear, raise.

GAP_BLUE_MAX_DEFAULT
Rule: if two trays merge as one, lower; if real trays get rejected (gap looks blue), raise.

BAND_FRACTION_DEFAULT
Rule: widen if trays drift left/right; narrow to speed up or avoid side clutter.

SMOOTH_WINDOW_DEFAULT
Rule: increase if rail lines flicker; decrease if it lags and misses short trays.

CLAHE_CLIP_LIMIT
Rule: raise a bit if edges are weak; drop if you see grain causing false lines.

B) Pouch counting (white-core method)

LAB_L_MIN
Rule: lower if shaded pouches disappear; raise if non-pouch bright bits enter.

LAB_AB_TOL
Rule: raise under warm lights/yellowish film; lower to fight tinted noise.

DT_PEAK_REL
Rule: lower to split merged pouches; raise to avoid over-splitting.

WHITE_OPEN_K / WHITE_CLOSE_K
Rule: increase close to bridge small gaps; increase open to kill speckles. Keep odd (3/5/7).

TRAY_RIM_MARGIN
Rule: reduce slightly to keep edge pouches; raise if rim glare becomes “white”.

MIN_POUCH_AREA_FRACTION / MIN_POUCH_AREA_ABSOLUTE
Rule: raise min if logos/noise counted; lower if tiny far-camera pouches get dropped.

C) Dominant color (labeling/QA, not counting)

WHITE_S_MAX
Rule: raise if white gate rejects cream/printed areas; lower if colored backgrounds leak in.

WHITE_V_MIN
Rule: lower if shadows block color detection; raise if dull backgrounds leak in.

WHITE_GATE_EXPANSION_FACTOR
Rule: raise to better cover stripes placed just off the white; lower to avoid tray contamination.

COLOR_KERNEL_SIZE / MORPH_…
Rule: bump up if you see pepper noise; reduce if logos break apart too much.

TRAY_BLUE_RANGE
Rule: widen if some blue plastic remains; narrow if it starts killing real blue ink.

"""




"""
Configuration file for v1_optimized.py - Crate Recognition CV System
=====================================================================

This file contains all configurable parameters used throughout the system.
Modify these values to adjust detection sensitivity, performance, appearance, and behavior.

Each parameter includes detailed comments explaining:
- What it controls
- Which functions use it
- How to tune it
- Recommended value ranges
"""

# =============================================================================
# COLOR DETECTION SETTINGS
# =============================================================================

# HSV Color Ranges for Pouch Detection
# Used by: create_color_mask_fast(), detect_pouches_and_colors_fast()
# Format: [H_min, S_min, V_min], [H_max, S_max, V_max]
# Hue: 0-179, Saturation: 0-255, Value: 0-255
COLOR_RANGES = {
    # Red pouches need two ranges due to HSV hue wrap-around (0° = 180°)
    "red1":   ([0,   70, 80], [10, 255, 255]),    # Lower red hue range (0-10°)
    "red2":   ([170, 70, 80], [180,255, 255]),    # Upper red hue range (170-180°)
    
    # Blue pouches - narrowed to avoid tray plastic false positives
    "blue":   ([103, 95, 95], [120,255, 255]),    # Tight blue range for pouches only
    
    # Green pouches
    "green":  ([40,  60, 60], [85, 255, 255]),    # Standard green range
    
    # Yellow pouches  
    "yellow": ([20,  80, 80], [35, 255, 255]),    # Standard yellow range
}

# HSV Range for Tray Plastic Detection (to exclude from analysis)
# Used by: detect_pouches_and_colors_fast() to create exclude_mask
# Purpose: Identifies blue tray plastic to prevent false pouch detection
# Tuning: Widen range if tray plastic isn't being excluded, narrow if legitimate pouches are excluded
TRAY_BLUE_RANGE = ([90, 60, 40], [135, 255, 255])

# White Gate Parameters (for filtering color detection to pouch areas only)
# Used by: detect_pouches_and_colors_fast() to create white_gate mask
# Purpose: Ensures color detection only occurs on white pouch backgrounds, not tray walls
WHITE_S_MAX = 110          # Maximum saturation for "white" areas (0-255). Higher = more colored whites allowed
WHITE_V_MIN = 160          # Minimum value (brightness) for "white" areas (0-255). Lower = darker whites allowed

# White Gate Expansion Factor
# Used by: detect_pouches_and_colors_fast() to grow white areas around colored print
# Purpose: Covers colored text/logos on white pouches
# Formula: gate_px = max(4, int(WHITE_GATE_EXPANSION_FACTOR * min(H, W)))
WHITE_GATE_EXPANSION_FACTOR = 0.015    # 1.5% of shorter tray dimension. Increase to cover more area around white

# =============================================================================
# WHITE-BASED POUCH COUNTING SETTINGS (NEW)
# =============================================================================

# Pouch Counting Mode Selection
# Used by: detect_pouches_and_colors_fast() to determine counting method
# Options: "white" (white-core based), "color" (existing color-based), "auto" (fallback)
POUCH_COUNT_MODE = "white"             # Primary counting method

# LAB Color Space Parameters for White Detection
# Used by: count_pouches_from_white() for robust white detection
# Purpose: Detects white pouch cores using LAB lightness and neutral color
LAB_L_MIN = 148               # Minimum lightness for white (100-160 typical, lowered significantly)
LAB_AB_TOL = 24                        # |a-128| and |b-128| tolerance for neutral color (20-40, increased significantly)

# Distance Transform Watershed Parameters
# Used by: count_pouches_from_white() for splitting touching pouches
# Purpose: Separates individual pouches using shadow valleys between them
DT_PEAK_REL = 0.30                     # Marker threshold as fraction of max distance (0.25-0.45, loweredfor more splitting)

# White Detection Morphology Parameters
# Used by: count_pouches_from_white() for cleaning white mask
# Purpose: Removes noise and connects pouch cores before watershed
WHITE_OPEN_K = 3                       # Morphology open kernel size (odd: 3/5)
WHITE_CLOSE_K = 3                      # Morphology close kernel size (odd: 3/5/7)

# =============================================================================
# GEOMETRIC PARAMETERS  
# =============================================================================

# Tray Rim Margin (pixels to ignore near tray edges)
# Used by: detect_pouches_and_colors_fast() to create rim exclusion mask
# Purpose: Ignores tray edge pixels that may contain plastic color bleed
# Tuning: Increase if getting false positives from tray edges, decrease if missing edge pouches
TRAY_RIM_MARGIN = 18       # pixels

# ROI Margin (extra pixels around detected tray bounds)  
# Used by: _tray_roi_bounds() for consistent cropping/drawing bounds
# Purpose: Adds buffer around tray detection for better visualization and processing
ROI_MARGIN_DEFAULT = 10    # pixels - used throughout for tray ROI extraction

# Pouch Size Constraints (as fractions of total tray ROI area)
# Used by: _accumulate_from_masks() to filter contours by size
# Purpose: Eliminates noise (too small) and tray walls (too large) from pouch detection
MIN_POUCH_AREA_FRACTION = 0.0015   # 0.05% of ROI area - minimum pouch size (reduced for white detection)
MAX_POUCH_AREA_FRACTION = 0.12    # 8% of ROI area - maximum pouch size (reduced to avoid large merged areas)
MIN_POUCH_AREA_ABSOLUTE = 120      # Absolute minimum area in pixels (increased for more realistic pouch sizes)

# =============================================================================
# IMAGE PROCESSING SETTINGS
# =============================================================================

# CLAHE (Contrast Limited Adaptive Histogram Equalization) Parameters
# Used by: get_clahe() for adaptive contrast enhancement
# Purpose: Improves contrast in varying lighting conditions
CLAHE_CLIP_LIMIT = 2.5             # Contrast limiting factor (1.0-4.0 recommended)
CLAHE_TILE_GRID_SIZE = (8, 8)      # Grid size for local contrast enhancement

# Morphological Operation Kernel Sizes
# Used by: get_color_kernel() and various morphological operations
# Purpose: Noise removal, gap filling, shape enhancement
COLOR_KERNEL_SIZE = 3              # Size for color detection kernels (3x3, 5x5, 7x7)
MORPH_CLOSE_ITERATIONS = 1         # Iterations for closing operations (gap filling)
MORPH_OPEN_ITERATIONS = 1          # Iterations for opening operations (noise removal)

# Blue Run Analysis Parameters  
# Used by: longest_blue_runs_vectorized() for tray detection
BAND_FRACTION_DEFAULT = 0.6        # Fraction of image width used as central detection band
SMOOTH_WINDOW_DEFAULT = 21         # Window size for smoothing blue intensity profiles

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Frame Processing Parameters
# Used by: run_optimized() main processing loop
TARGET_FPS_DEFAULT = 45            # Target display FPS (not processing FPS)
SKIP_FRAMES_DEFAULT = 2            # Process every Nth frame for performance (1=every frame)

# Video Gate Parameters (for tray transition detection)
# Used by: VideoGateCounter for state machine transitions
GATE_HEIGHT_DEFAULT = 80           # Bottom gate height in pixels
ENTER_FRAMES_DEFAULT = 3           # Frames needed to confirm TRAY_START state
EXIT_FRAMES_DEFAULT = 5            # Frames needed to confirm TRAY_END state

# Detection Thresholds
# Used by: detect_trays_optimized() for tray validation
TRAY_MIN_HEIGHT_DEFAULT = 170 #250      # Minimum expected tray height in pixels
TRAY_MAX_HEIGHT_DEFAULT = 230 #430      # Maximum expected tray height in pixels  
SOLID_FRACTION_DEFAULT = 0.35 #0.45      # Solid-band width fraction for detection
GAP_BLUE_MAX_DEFAULT = 0.20 #0.30        # Maximum blue fraction allowed in gaps between trays

# =============================================================================
# UI/DISPLAY SETTINGS
# =============================================================================

# Side Panel Layout
# Used by: create_side_panel_layout() for information display
PANEL_WIDTH = 350                  # Fixed width of right-side information panel
PANEL_BACKGROUND_COLOR = 40        # Gray level (0-255) for panel background

# Typography Settings  
# Used by: create_side_panel_layout() and put_label() for text rendering
# Note: cv2 font constants are integers, defined in main script
FONT_TYPE = 0  # cv2.FONT_HERSHEY_SIMPLEX     
FONT_TYPE_MODERN = 2  # cv2.FONT_HERSHEY_DUPLEX 
FONT_SCALE_HEADER = 0.6            # Scale for panel headers  
FONT_SCALE_NORMAL = 0.45           # Scale for normal text
FONT_SCALE_SMALL = 0.4             # Scale for small text
FONT_THICKNESS = 1                 # Text thickness (1-3)
FONT_THICKNESS_HEADER = 2          # Header text thickness

# Modern Label Settings (for put_label function)
# Used by: put_label() for modern text rendering with backgrounds
LABEL_FONT_SCALE_DEFAULT = 0.6     # Default scale for put_label
LABEL_THICKNESS_DEFAULT = 1        # Default thickness for put_label
LABEL_PADDING_DEFAULT = 4          # Padding around text in background chip
LABEL_FG_COLOR_DEFAULT = (255, 255, 255)  # Default foreground color (white)
LABEL_BG_COLOR_DEFAULT = (0, 0, 0)        # Default background color (black)

# Layout Parameters
# Used by: create_side_panel_layout() for spacing and positioning
LINE_HEIGHT = 18                   # Vertical spacing between text lines
MARGIN_DEFAULT = 10                # Default margin for UI elements
HEADER_SPACING = 30                # Extra spacing after headers
SECTION_SPACING = 15               # Spacing between sections

# Corner Markers
# Used by: draw_tray_overlays() for tray boundary visualization
CORNER_MARKER_SIZE = 20            # Size of corner tick marks in pixels
CORNER_MARKER_THICKNESS = 5        # Thickness of corner lines

# =============================================================================
# VISUALIZATION COLORS (BGR format for OpenCV)
# =============================================================================

# Pouch Color Mapping for Visualization
# Used by: draw_pouch_overlays_fast() for color-coded dots
POUCH_VISUALIZATION_COLORS = {
    "red": (0, 0, 255),      # Red pouches -> Red markers
    "pink": (255, 0, 255),   # Pink pouches -> Magenta markers  
    "blue": (255, 0, 0),     # Blue pouches -> Blue markers
    "green": (0, 255, 0),    # Green pouches -> Green markers
    "yellow": (0, 255, 255), # Yellow pouches -> Cyan markers
}

# Tray Boundary Colors (cycling through detected trays)
# Used by: draw_tray_overlays() for different tray identification
TRAY_BOUNDARY_COLORS = [
    (0, 255, 0),    # Green  
    (255, 0, 0),    # Blue
    (0, 255, 255),  # Yellow  
    (255, 0, 255),  # Magenta
    (0, 165, 255),  # Orange
    (255, 255, 0),  # Cyan
]

# UI Colors
# Used by: create_side_panel_layout() for various UI elements
UI_COLOR_WHITE = (255, 255, 255)       # Standard white text
UI_COLOR_GRAY = (128, 128, 128)        # Gray for less important text
UI_COLOR_HEADER = (0, 255, 255)        # Cyan for headers
UI_COLOR_CONTROLS = (200, 200, 200)    # Light gray for control text

# =============================================================================
# FILE OUTPUT SETTINGS
# =============================================================================

# Output Directory Structure  
# Used by: run_optimized() for organizing saved files
OUTPUT_DIR_DEFAULT = "out"                    # Base output directory
TRAY_CROPS_CLEAN_SUBDIR = "tray_images_clean"  # Clean tray crops (no overlay)
TRAY_CROPS_ANNOTATED_SUBDIR = "tray_images_annotated"  # Annotated tray crops

# Image Save Settings
# Used by: save functions throughout the system
IMAGE_SAVE_QUALITY_JPEG = 90          # JPEG quality (0-100)
IMAGE_SAVE_FORMAT_DEFAULT = '.png'     # Default format for saved images

# =============================================================================
# ADVANCED TUNING NOTES
# =============================================================================

"""
PERFORMANCE TUNING:
- Increase SKIP_FRAMES_DEFAULT for better performance, decrease for more accuracy
- Adjust CLAHE_CLIP_LIMIT based on lighting conditions (higher for more contrast)
- Modify COLOR_KERNEL_SIZE for noise vs detail tradeoff (larger = less noise, less detail)

COLOR DETECTION TUNING:
- Widen COLOR_RANGES if missing pouches, narrow if getting false positives  
- Adjust WHITE_S_MAX/WHITE_V_MIN for different lighting conditions
- Increase WHITE_GATE_EXPANSION_FACTOR if colored text on pouches isn't detected
- Modify TRAY_RIM_MARGIN if edge detection is problematic

GEOMETRIC TUNING:  
- Adjust MIN/MAX_POUCH_AREA_FRACTION for different pouch sizes
- Modify TRAY_MIN/MAX_HEIGHT for different tray dimensions
- Change ROI_MARGIN_DEFAULT for tighter/looser cropping

UI CUSTOMIZATION:
- Modify PANEL_WIDTH for more/less information display
- Adjust FONT_SCALE_* for readability on different screen sizes
- Change colors in POUCH_VISUALIZATION_COLORS for different color schemes
"""
