#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration file for v1_optimized.py - Crate Recognition CV System
=====================================================================

This file contains all configurable parameters used throughout the system.
Modify these values to adjust detection sensitivity, performance, appearance, and behavior.

Each parameter includes detailed comments explaining:
- What it controls
- Which functions use it
- How to tune it
- Recommended value ranges
"""

# =============================================================================
# COLOR DETECTION SETTINGS
# =============================================================================

# HSV Color Ranges for Pouch Detection
# Used by: create_color_mask_fast(), detect_pouches_and_colors_fast()
# Format: [H_min, S_min, V_min], [H_max, S_max, V_max]
# Hue: 0-179, Saturation: 0-255, Value: 0-255
COLOR_RANGES = {
    # Red pouches need two ranges due to HSV hue wrap-around (0° = 180°)
    "red1":   ([0,   70, 80], [10, 255, 255]),    # Lower red hue range (0-10°)
    "red2":   ([170, 70, 80], [180,255, 255]),    # Upper red hue range (170-180°)
    
    # Blue pouches - narrowed to avoid tray plastic false positives
    "blue":   ([103, 95, 95], [120,255, 255]),    # Tight blue range for pouches only
    
    # Green pouches
    "green":  ([40,  60, 60], [85, 255, 255]),    # Standard green range
    
    # Yellow pouches  
    "yellow": ([20,  80, 80], [35, 255, 255]),    # Standard yellow range
}

# HSV Range for Tray Plastic Detection (to exclude from analysis)
# Used by: detect_pouches_and_colors_fast() to create exclude_mask
# Purpose: Identifies blue tray plastic to prevent false pouch detection
# Tuning: Widen range if tray plastic isn't being excluded, narrow if legitimate pouches are excluded
TRAY_BLUE_RANGE = ([90, 60, 40], [135, 255, 255])

# White Gate Parameters (for filtering color detection to pouch areas only)
# Used by: detect_pouches_and_colors_fast() to create white_gate mask
# Purpose: Ensures color detection only occurs on white pouch backgrounds, not tray walls
WHITE_S_MAX = 110          # Maximum saturation for "white" areas (0-255). Higher = more colored whites allowed
WHITE_V_MIN = 160          # Minimum value (brightness) for "white" areas (0-255). Lower = darker whites allowed

# White Gate Expansion Factor  
# Used by: detect_pouches_and_colors_fast() to grow white areas around colored print
# Purpose: Covers colored text/logos on white pouches
# Formula: gate_px = max(4, int(WHITE_GATE_EXPANSION_FACTOR * min(H, W)))
WHITE_GATE_EXPANSION_FACTOR = 0.015    # 1.5% of shorter tray dimension. Increase to cover more area around white

# =============================================================================
# GEOMETRIC PARAMETERS  
# =============================================================================

# Tray Rim Margin (pixels to ignore near tray edges)
# Used by: detect_pouches_and_colors_fast() to create rim exclusion mask
# Purpose: Ignores tray edge pixels that may contain plastic color bleed
# Tuning: Increase if getting false positives from tray edges, decrease if missing edge pouches
TRAY_RIM_MARGIN = 18       # pixels

# ROI Margin (extra pixels around detected tray bounds)  
# Used by: _tray_roi_bounds() for consistent cropping/drawing bounds
# Purpose: Adds buffer around tray detection for better visualization and processing
ROI_MARGIN_DEFAULT = 10    # pixels - used throughout for tray ROI extraction

# Pouch Size Constraints (as fractions of total tray ROI area)
# Used by: _accumulate_from_masks() to filter contours by size
# Purpose: Eliminates noise (too small) and tray walls (too large) from pouch detection
MIN_POUCH_AREA_FRACTION = 0.002    # 0.2% of ROI area - minimum pouch size
MAX_POUCH_AREA_FRACTION = 0.12     # 12% of ROI area - maximum pouch size  
MIN_POUCH_AREA_ABSOLUTE = 120      # Absolute minimum area in pixels (fallback for small ROIs)

# =============================================================================
# IMAGE PROCESSING SETTINGS
# =============================================================================

# CLAHE (Contrast Limited Adaptive Histogram Equalization) Parameters
# Used by: get_clahe() for adaptive contrast enhancement
# Purpose: Improves contrast in varying lighting conditions
CLAHE_CLIP_LIMIT = 2.5             # Contrast limiting factor (1.0-4.0 recommended)
CLAHE_TILE_GRID_SIZE = (8, 8)      # Grid size for local contrast enhancement

# Morphological Operation Kernel Sizes
# Used by: get_color_kernel() and various morphological operations
# Purpose: Noise removal, gap filling, shape enhancement
COLOR_KERNEL_SIZE = 3              # Size for color detection kernels (3x3, 5x5, 7x7)
MORPH_CLOSE_ITERATIONS = 1         # Iterations for closing operations (gap filling)
MORPH_OPEN_ITERATIONS = 1          # Iterations for opening operations (noise removal)

# Blue Run Analysis Parameters  
# Used by: longest_blue_runs_vectorized() for tray detection
BAND_FRACTION_DEFAULT = 0.6        # Fraction of image width used as central detection band
SMOOTH_WINDOW_DEFAULT = 21         # Window size for smoothing blue intensity profiles

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Frame Processing Parameters
# Used by: run_optimized() main processing loop
TARGET_FPS_DEFAULT = 45            # Target display FPS (not processing FPS)
SKIP_FRAMES_DEFAULT = 2            # Process every Nth frame for performance (1=every frame)

# Video Gate Parameters (for tray transition detection)
# Used by: VideoGateCounter for state machine transitions
GATE_HEIGHT_DEFAULT = 80           # Bottom gate height in pixels
ENTER_FRAMES_DEFAULT = 3           # Frames needed to confirm TRAY_START state
EXIT_FRAMES_DEFAULT = 5            # Frames needed to confirm TRAY_END state

# Detection Thresholds
# Used by: detect_trays_optimized() for tray validation
TRAY_MIN_HEIGHT_DEFAULT = 170 #250      # Minimum expected tray height in pixels
TRAY_MAX_HEIGHT_DEFAULT = 230 #430      # Maximum expected tray height in pixels  
SOLID_FRACTION_DEFAULT = 0.35 #0.45      # Solid-band width fraction for detection
GAP_BLUE_MAX_DEFAULT = 0.20 #0.30        # Maximum blue fraction allowed in gaps between trays

# =============================================================================
# UI/DISPLAY SETTINGS
# =============================================================================

# Side Panel Layout
# Used by: create_side_panel_layout() for information display
PANEL_WIDTH = 350                  # Fixed width of right-side information panel
PANEL_BACKGROUND_COLOR = 40        # Gray level (0-255) for panel background

# Typography Settings  
# Used by: create_side_panel_layout() and put_label() for text rendering
# Note: cv2 font constants are integers, defined in main script
FONT_TYPE = 0  # cv2.FONT_HERSHEY_SIMPLEX     
FONT_TYPE_MODERN = 2  # cv2.FONT_HERSHEY_DUPLEX 
FONT_SCALE_HEADER = 0.6            # Scale for panel headers  
FONT_SCALE_NORMAL = 0.45           # Scale for normal text
FONT_SCALE_SMALL = 0.4             # Scale for small text
FONT_THICKNESS = 1                 # Text thickness (1-3)
FONT_THICKNESS_HEADER = 2          # Header text thickness

# Modern Label Settings (for put_label function)
# Used by: put_label() for modern text rendering with backgrounds
LABEL_FONT_SCALE_DEFAULT = 0.6     # Default scale for put_label
LABEL_THICKNESS_DEFAULT = 1        # Default thickness for put_label
LABEL_PADDING_DEFAULT = 4          # Padding around text in background chip
LABEL_FG_COLOR_DEFAULT = (255, 255, 255)  # Default foreground color (white)
LABEL_BG_COLOR_DEFAULT = (0, 0, 0)        # Default background color (black)

# Layout Parameters
# Used by: create_side_panel_layout() for spacing and positioning
LINE_HEIGHT = 18                   # Vertical spacing between text lines
MARGIN_DEFAULT = 10                # Default margin for UI elements
HEADER_SPACING = 30                # Extra spacing after headers
SECTION_SPACING = 15               # Spacing between sections

# Corner Markers
# Used by: draw_tray_overlays() for tray boundary visualization
CORNER_MARKER_SIZE = 20            # Size of corner tick marks in pixels
CORNER_MARKER_THICKNESS = 5        # Thickness of corner lines

# =============================================================================
# VISUALIZATION COLORS (BGR format for OpenCV)
# =============================================================================

# Pouch Color Mapping for Visualization
# Used by: draw_pouch_overlays_fast() for color-coded dots
POUCH_VISUALIZATION_COLORS = {
    "red": (0, 0, 255),      # Red pouches -> Red markers
    "pink": (255, 0, 255),   # Pink pouches -> Magenta markers  
    "blue": (255, 0, 0),     # Blue pouches -> Blue markers
    "green": (0, 255, 0),    # Green pouches -> Green markers
    "yellow": (0, 255, 255), # Yellow pouches -> Cyan markers
}

# Tray Boundary Colors (cycling through detected trays)
# Used by: draw_tray_overlays() for different tray identification
TRAY_BOUNDARY_COLORS = [
    (0, 255, 0),    # Green  
    (255, 0, 0),    # Blue
    (0, 255, 255),  # Yellow  
    (255, 0, 255),  # Magenta
    (0, 165, 255),  # Orange
    (255, 255, 0),  # Cyan
]

# UI Colors
# Used by: create_side_panel_layout() for various UI elements
UI_COLOR_WHITE = (255, 255, 255)       # Standard white text
UI_COLOR_GRAY = (128, 128, 128)        # Gray for less important text
UI_COLOR_HEADER = (0, 255, 255)        # Cyan for headers
UI_COLOR_CONTROLS = (200, 200, 200)    # Light gray for control text

# =============================================================================
# FILE OUTPUT SETTINGS
# =============================================================================

# Output Directory Structure  
# Used by: run_optimized() for organizing saved files
OUTPUT_DIR_DEFAULT = "out"                    # Base output directory
TRAY_CROPS_CLEAN_SUBDIR = "tray_images_clean"  # Clean tray crops (no overlay)
TRAY_CROPS_ANNOTATED_SUBDIR = "tray_images_annotated"  # Annotated tray crops

# Image Save Settings
# Used by: save functions throughout the system
IMAGE_SAVE_QUALITY_JPEG = 90          # JPEG quality (0-100)
IMAGE_SAVE_FORMAT_DEFAULT = '.png'     # Default format for saved images

# =============================================================================
# ADVANCED TUNING NOTES
# =============================================================================

"""
PERFORMANCE TUNING:
- Increase SKIP_FRAMES_DEFAULT for better performance, decrease for more accuracy
- Adjust CLAHE_CLIP_LIMIT based on lighting conditions (higher for more contrast)
- Modify COLOR_KERNEL_SIZE for noise vs detail tradeoff (larger = less noise, less detail)

COLOR DETECTION TUNING:
- Widen COLOR_RANGES if missing pouches, narrow if getting false positives  
- Adjust WHITE_S_MAX/WHITE_V_MIN for different lighting conditions
- Increase WHITE_GATE_EXPANSION_FACTOR if colored text on pouches isn't detected
- Modify TRAY_RIM_MARGIN if edge detection is problematic

GEOMETRIC TUNING:  
- Adjust MIN/MAX_POUCH_AREA_FRACTION for different pouch sizes
- Modify TRAY_MIN/MAX_HEIGHT for different tray dimensions
- Change ROI_MARGIN_DEFAULT for tighter/looser cropping

UI CUSTOMIZATION:
- Modify PANEL_WIDTH for more/less information display
- Adjust FONT_SCALE_* for readability on different screen sizes
- Change colors in POUCH_VISUALIZATION_COLORS for different color schemes
"""
