#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug script to understand why white detection is finding so few pouches
"""

import cv2
import numpy as np
import sys
from pathlib import Path

# Import the functions
from config import *

def debug_white_detection_step_by_step(bgr_roi):
    """Debug white detection step by step with visualizations"""
    
    if bgr_roi.shape[0] < 20 or bgr_roi.shape[1] < 20:
        print("Image too small")
        return
    
    H, W = bgr_roi.shape[:2]
    print(f"Image size: {W}x{H}")
    
    # Step 1: LAB color space analysis
    lab = cv2.cvtColor(bgr_roi, cv2.COLOR_BGR2LAB)
    L, A, B = cv2.split(lab)
    
    print(f"LAB L channel stats: min={L.min()}, max={L.max()}, mean={L.mean():.1f}")
    print(f"LAB A channel stats: min={A.min()}, max={A.max()}, mean={A.mean():.1f}")
    print(f"LAB B channel stats: min={B.min()}, max={B.max()}, mean={B.mean():.1f}")
    
    # LAB neutral color detection
    ab_neutral = (np.abs(A.astype(np.int16) - 128) <= LAB_AB_TOL) & \
                 (np.abs(B.astype(np.int16) - 128) <= LAB_AB_TOL)
    lab_white = (L >= LAB_L_MIN) & ab_neutral
    
    print(f"LAB white pixels: {lab_white.sum()} ({lab_white.sum()/(H*W)*100:.1f}%)")
    
    # Step 2: HSV white detection
    hsv = cv2.cvtColor(bgr_roi, cv2.COLOR_BGR2HSV)
    hsv_white = cv2.inRange(hsv, (0, 0, WHITE_V_MIN), (179, WHITE_S_MAX, 255)).astype(bool)
    
    print(f"HSV white pixels: {hsv_white.sum()} ({hsv_white.sum()/(H*W)*100:.1f}%)")
    
    # Step 3: Combined white detection
    white = (lab_white & hsv_white)
    print(f"Combined white pixels: {white.sum()} ({white.sum()/(H*W)*100:.1f}%)")
    
    # Step 4: Exclude blue rim
    tray_blue = cv2.inRange(hsv, np.array(TRAY_BLUE_RANGE[0], np.uint8), 
                           np.array(TRAY_BLUE_RANGE[1], np.uint8)).astype(bool)
    rim = np.zeros((H, W), np.uint8)
    m = TRAY_RIM_MARGIN
    rim[:m, :] = rim[-m:, :] = rim[:, :m] = rim[:, -m:] = 255
    exclude = (tray_blue & (rim > 0))
    white[exclude] = False
    
    print(f"White after exclusion: {white.sum()} ({white.sum()/(H*W)*100:.1f}%)")
    
    # Step 5: Morphological operations
    k_open = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (WHITE_OPEN_K, WHITE_OPEN_K))
    k_close = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (WHITE_CLOSE_K, WHITE_CLOSE_K))
    white_u8 = (white.astype(np.uint8) * 255)
    
    white_opened = cv2.morphologyEx(white_u8, cv2.MORPH_OPEN, k_open, iterations=1)
    white_final = cv2.morphologyEx(white_opened, cv2.MORPH_CLOSE, k_close, iterations=1)
    
    print(f"White after morphology: {cv2.countNonZero(white_final)} pixels")
    
    # Step 6: Distance transform
    dist = cv2.distanceTransform(white_final, cv2.DIST_L2, 5)
    if dist.max() == 0:
        print("No white areas found for distance transform")
        return
    
    print(f"Distance transform max: {dist.max():.2f}")
    
    # Step 7: Watershed markers
    threshold = DT_PEAK_REL * dist.max()
    print(f"Peak threshold: {threshold:.2f}")
    
    _, peaks = cv2.threshold(dist, threshold, 255, cv2.THRESH_BINARY)
    peaks = peaks.astype(np.uint8)
    num_markers, markers = cv2.connectedComponents(peaks)
    
    print(f"Number of markers found: {num_markers - 1}")  # -1 because 0 is background

    # Step 8: Watershed and area filtering
    markers = markers + 1  # 0 is reserved for background
    markers[white_final == 0] = 0

    # Watershed
    ws_img = bgr_roi.copy()
    cv2.watershed(ws_img, markers)

    # Area filtering
    roi_area = H * W
    min_area = max(MIN_POUCH_AREA_ABSOLUTE, roi_area * MIN_POUCH_AREA_FRACTION)
    max_area = roi_area * MAX_POUCH_AREA_FRACTION

    print(f"ROI area: {roi_area}")
    print(f"Min area threshold: {min_area:.0f} ({MIN_POUCH_AREA_FRACTION*100:.3f}%)")
    print(f"Max area threshold: {max_area:.0f} ({MAX_POUCH_AREA_FRACTION*100:.1f}%)")

    valid_centers = []
    labels = np.unique(markers)
    for lbl in labels:
        if lbl <= 1:  # 0 = background, 1 = unknown rim from watershed
            continue
        mask = (markers == lbl)
        area = int(mask.sum())
        print(f"  Label {lbl}: area = {area}")
        if area < min_area:
            print(f"    -> REJECTED (too small, < {min_area:.0f})")
        elif area > max_area:
            print(f"    -> REJECTED (too large, > {max_area:.0f})")
        else:
            ys, xs = np.nonzero(mask)
            if xs.size == 0:
                print(f"    -> REJECTED (no pixels)")
                continue
            cx = int(xs.mean())
            cy = int(ys.mean())
            valid_centers.append((cx, cy))
            print(f"    -> ACCEPTED at ({cx}, {cy})")

    print(f"Final valid centers: {len(valid_centers)}")

    # Visualizations
    cv2.imshow("Original", bgr_roi)
    cv2.imshow("LAB L Channel", L)
    cv2.imshow("LAB White Mask", (lab_white * 255).astype(np.uint8))
    cv2.imshow("HSV White Mask", (hsv_white * 255).astype(np.uint8))
    cv2.imshow("Combined White", (white * 255).astype(np.uint8))
    cv2.imshow("After Morphology", white_final)
    cv2.imshow("Distance Transform", (dist / dist.max() * 255).astype(np.uint8))
    cv2.imshow("Peaks", peaks)
    
    # Show parameter info
    info_img = np.zeros((300, 500, 3), dtype=np.uint8)
    y = 30
    cv2.putText(info_img, f"LAB_L_MIN: {LAB_L_MIN}", (10, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    y += 30
    cv2.putText(info_img, f"LAB_AB_TOL: {LAB_AB_TOL}", (10, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    y += 30
    cv2.putText(info_img, f"DT_PEAK_REL: {DT_PEAK_REL}", (10, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    y += 30
    cv2.putText(info_img, f"WHITE_V_MIN: {WHITE_V_MIN}", (10, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    y += 30
    cv2.putText(info_img, f"WHITE_S_MAX: {WHITE_S_MAX}", (10, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    y += 50
    cv2.putText(info_img, f"L stats: {L.min()}-{L.max()} (avg {L.mean():.0f})", (10, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
    y += 25
    cv2.putText(info_img, f"Combined white: {white.sum()} pixels", (10, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
    y += 25
    cv2.putText(info_img, f"Markers found: {num_markers - 1}", (10, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
    
    cv2.imshow("Debug Info", info_img)
    
    print("\nPress any key to continue...")
    cv2.waitKey(0)
    cv2.destroyAllWindows()

def main():
    if len(sys.argv) < 2:
        print("Usage: python debug_white_detection.py <image_path>")
        return
    
    image_path = sys.argv[1]
    img = cv2.imread(image_path)
    if img is None:
        print(f"Could not load image: {image_path}")
        return
    
    print("=== WHITE DETECTION DEBUG ===")
    print(f"Image: {image_path}")
    print(f"Current parameters:")
    print(f"  LAB_L_MIN: {LAB_L_MIN}")
    print(f"  LAB_AB_TOL: {LAB_AB_TOL}")
    print(f"  DT_PEAK_REL: {DT_PEAK_REL}")
    print(f"  WHITE_V_MIN: {WHITE_V_MIN}")
    print(f"  WHITE_S_MAX: {WHITE_S_MAX}")
    print("-" * 40)
    
    debug_white_detection_step_by_step(img)

if __name__ == "__main__":
    main()
