#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo script showing dual counting (color-based vs white-based) side by side
"""

import cv2
import numpy as np
import argparse
from pathlib import Path

# Import the updated functions
from v1_optimized import detect_pouches_and_colors_fast, count_pouches_from_white
from config import *

def create_comparison_view(bgr_roi, color_centers, white_centers, color_count, white_count, dominant_color):
    """Create a side-by-side comparison view"""
    
    h, w = bgr_roi.shape[:2]
    
    # Create side-by-side layout
    comparison = np.zeros((h, w * 2 + 20, 3), dtype=np.uint8)
    
    # Left side: Color-based detection
    left_img = bgr_roi.copy()
    for px, py, color in color_centers:
        color_bgr = POUCH_VISUALIZATION_COLORS.get(color, (128, 128, 128))
        cv2.circle(left_img, (px, py), 8, color_bgr, -1)
        cv2.circle(left_img, (px, py), 10, (255, 255, 255), 2)
    
    # Add title and count
    cv2.putText(left_img, f"COLOR-BASED: {color_count}", (10, 30), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
    cv2.putText(left_img, f"Dominant: {dominant_color}", (10, 60), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
    
    # Right side: White-based detection
    right_img = bgr_roi.copy()
    for px, py in white_centers:
        cv2.circle(right_img, (px, py), 6, (255, 255, 255), -1)
        cv2.circle(right_img, (px, py), 8, (0, 0, 0), 2)
    
    # Add title and count
    cv2.putText(right_img, f"WHITE-BASED: {white_count}", (10, 30), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
    cv2.putText(right_img, "Core Detection", (10, 60), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
    
    # Place images side by side
    comparison[:, :w] = left_img
    comparison[:, w+20:] = right_img
    
    # Add separator line
    cv2.line(comparison, (w+10, 0), (w+10, h-1), (255, 255, 255), 2)
    
    return comparison

def demo_on_image(image_path):
    """Demo dual counting on a single image"""
    
    print(f"Loading image: {image_path}")
    img = cv2.imread(str(image_path))
    if img is None:
        print(f"Could not load image: {image_path}")
        return
    
    print(f"Image size: {img.shape[1]}x{img.shape[0]}")
    
    # Run dual detection
    try:
        color_counts, color_pouches, white_pouches, dominant_color, color_centers, white_centers = detect_pouches_and_colors_fast(img)
        
        print(f"\n=== COMPARISON RESULTS ===")
        print(f"Color-based count: {color_pouches}")
        print(f"White-based count: {white_pouches}")
        print(f"Difference: {abs(color_pouches - white_pouches)}")
        print(f"Dominant color: {dominant_color}")
        print(f"Color breakdown: {dict(color_counts)}")
        
        # Create comparison visualization
        comparison = create_comparison_view(img, color_centers, white_centers, 
                                          color_pouches, white_pouches, dominant_color)
        
        # Show results
        cv2.imshow("Original Image", img)
        cv2.imshow("Color vs White Detection Comparison", comparison)
        
        # Also show white mask for debugging
        _, _, white_mask = count_pouches_from_white(img)
        cv2.imshow("White Detection Mask", white_mask)
        
        print(f"\nPress any key to continue, 'q' to quit...")
        key = cv2.waitKey(0) & 0xFF
        cv2.destroyAllWindows()
        
        return key != ord('q')
        
    except Exception as e:
        print(f"Error during processing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main demo function"""
    
    parser = argparse.ArgumentParser(description="Demo dual counting methods")
    parser.add_argument("--input", type=str, help="Image file path")
    parser.add_argument("--dir", type=str, help="Directory containing images")
    args = parser.parse_args()
    
    print("=== DUAL COUNTING DEMO ===")
    print("This demo shows color-based vs white-based pouch counting side by side")
    print(f"Configuration: LAB_L_MIN={LAB_L_MIN}, LAB_AB_TOL={LAB_AB_TOL}, DT_PEAK_REL={DT_PEAK_REL}")
    print("-" * 60)
    
    images_to_test = []
    
    if args.input:
        # Single image
        images_to_test = [Path(args.input)]
    elif args.dir:
        # Directory of images
        dir_path = Path(args.dir)
        if dir_path.exists():
            for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
                images_to_test.extend(dir_path.glob(ext))
        else:
            print(f"Directory not found: {args.dir}")
            return
    else:
        # Auto-find images
        test_locations = [
            Path("Images"),
            Path("out/tray_images_clean"),
            Path("out/tray_images_annotated"),
            Path(".")
        ]
        
        for location in test_locations:
            if location.exists():
                for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
                    images_to_test.extend(location.glob(ext))
                if images_to_test:
                    break
    
    if not images_to_test:
        print("No images found. Usage:")
        print("  python demo_dual_counting.py --input image.jpg")
        print("  python demo_dual_counting.py --dir Images/")
        return
    
    print(f"Found {len(images_to_test)} images to test")
    
    # Test each image
    for i, img_path in enumerate(images_to_test[:10]):  # Limit to first 10
        print(f"\n[{i+1}/{min(len(images_to_test), 10)}] Testing: {img_path.name}")
        
        if not demo_on_image(img_path):
            print("Demo stopped by user")
            break
    
    print("\nDemo completed!")

if __name__ == "__main__":
    main()
