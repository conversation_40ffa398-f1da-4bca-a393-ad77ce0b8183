#!/usr/bin/env python3
"""
Enhanced SKU Crate Counter with Robust Tray Detection

This is an enhanced version of sku_crate_counter.py with:
- Robust multi-method tray detection
- Per-tray pouch counting to avoid cross-contamination
- Improved tray bounding box visualization
- Better debugging capabilities

Key Improvements:
1. Multiple tray detection methods (Color-based, Contour-geometry, Edge-Hough, Hybrid)
2. Per-tray pouch detection prevents counting pouches from adjacent trays
3. Tighter and more robust tray bounding boxes
4. Better visualization and debugging tools

Usage: Same as original sku_crate_counter.py
"""

# Import the enhanced tray detection module
from enhanced_tray_detection import (
    EnhancedTrayDetector, DetectionMethod, TrayDetectionConfig, 
    enhanced_find_trays, create_tray_detector
)

# Import everything from the original file
import sys
import os

# Add the current directory to path to import the original module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import all functions and classes from the original sku_crate_counter
from sku_crate_counter import *

# Override the find_trays function with enhanced version
def find_trays_enhanced(frame, tray_mask=None, learner=None, max_trays=2, method="hybrid"):
    """
    Enhanced tray detection function that replaces the original find_trays
    
    Args:
        frame: Input frame
        tray_mask: Original tray mask (for compatibility - not used in enhanced version)
        learner: Original tray learner (for compatibility - not used in enhanced version) 
        max_trays: Maximum number of trays to detect
        method: Detection method ("hybrid", "color_based", "contour_geometry", "edge_hough")
        
    Returns:
        List of tray bounding boxes as (x, y, width, height) tuples
    """
    return enhanced_find_trays(frame, tray_mask, method, max_trays)

# Create global enhanced tray detector
enhanced_detector = create_tray_detector(method="hybrid", debug=False)

def enhanced_per_tray_pouch_detection(frame, tray_boxes, masks, dominant_color, 
                                    detection_method="auto", show_markers=True):
    """
    Enhanced per-tray pouch detection that isolates counting to individual trays
    
    Args:
        frame: Input frame
        tray_boxes: List of tray bounding boxes
        masks: Color masks dictionary
        dominant_color: Dominant color detected
        detection_method: Pouch detection method to use
        show_markers: Whether to show pouch markers
        
    Returns:
        Dictionary mapping tray_index -> (pouch_count, visualization, pouch_data)
    """
    
    def pouch_detection_wrapper(tray_crop, show_markers=True):
        """Wrapper function that calls the appropriate pouch detection method"""
        
        # Create masks for this tray region only
        tray_h, tray_w = tray_crop.shape[:2]
        tray_masks = {}
        
        # Generate masks for the tray crop
        try:
            tray_masks = get_masks(tray_crop, performance_mode=False)
        except:
            # Fallback to empty masks
            tray_masks = {k: np.zeros((tray_h, tray_w), dtype=np.uint8) for k in masks.keys()}
        
        # Choose detection method based on current settings
        if detection_method == "truthful" or (detection_method == "auto" and dominant_color != "none"):
            try:
                return truthful_pouch_count(tray_crop, tray_masks, dominant_color, show_markers)
            except:
                # Fallback to hybrid detection
                pass
        
        if detection_method == "advanced":
            try:
                return ultra_sensitive_green_detection(tray_crop, tray_masks, show_markers, False)[:3]
            except:
                pass
        
        if detection_method == "traditional":
            try:
                return approx_pouch_count(tray_crop, tray_masks.get("tray", np.zeros((tray_h, tray_w), dtype=np.uint8)), show_markers)
            except:
                pass
        
        # Default: hybrid detection
        try:
            return hybrid_pouch_detection(tray_crop, tray_masks, dominant_color, show_markers)
        except:
            # Final fallback
            return 0, tray_crop.copy(), []
    
    # Use enhanced detector for per-tray pouch detection
    return enhanced_detector.detect_pouches_per_tray(
        frame, tray_boxes, pouch_detection_wrapper, show_markers
    )

def draw_enhanced_tray_visualization(vis, tray_boxes, per_tray_results=None, method_name="Enhanced"):
    """
    Draw enhanced tray visualization with better styling and information
    
    Args:
        vis: Visualization image to draw on
        tray_boxes: List of tray bounding boxes
        per_tray_results: Dictionary of per-tray pouch detection results
        method_name: Name of the detection method used
    """
    
    # Enhanced color palette
    colors = [
        (255, 100, 100),  # Light Red
        (100, 255, 100),  # Light Green  
        (100, 100, 255),  # Light Blue
        (255, 255, 100),  # Light Yellow
        (255, 100, 255),  # Light Magenta
        (100, 255, 255),  # Light Cyan
        (255, 150, 100),  # Orange
        (150, 100, 255),  # Purple
    ]
    
    for i, (x, y, w, h) in enumerate(tray_boxes):
        color = colors[i % len(colors)]
        
        # Draw thick tray bounding box
        cv2.rectangle(vis, (x, y), (x + w, y + h), color, 4)
        
        # Draw corner indicators for better visibility
        corner_size = 20
        # Top-left corner
        cv2.line(vis, (x, y), (x + corner_size, y), color, 6)
        cv2.line(vis, (x, y), (x, y + corner_size), color, 6)
        # Top-right corner  
        cv2.line(vis, (x + w, y), (x + w - corner_size, y), color, 6)
        cv2.line(vis, (x + w, y), (x + w, y + corner_size), color, 6)
        # Bottom-left corner
        cv2.line(vis, (x, y + h), (x + corner_size, y + h), color, 6)
        cv2.line(vis, (x, y + h), (x, y + h - corner_size), color, 6)
        # Bottom-right corner
        cv2.line(vis, (x + w, y + h), (x + w - corner_size, y + h), color, 6)
        cv2.line(vis, (x + w, y + h), (x + w, y + h - corner_size), color, 6)
        
        # Enhanced tray label with pouch count
        label = f"TRAY {i+1}"
        if per_tray_results and i in per_tray_results:
            pouch_count, _, _ = per_tray_results[i]
            label += f" [{pouch_count} POUCHES]"
        
        # Add dimensions
        dim_label = f"{w}x{h}px"
        
        # Draw label background
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)[0]
        dim_size = cv2.getTextSize(dim_label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
        
        # Main label background
        cv2.rectangle(vis, (x, y - label_size[1] - 15), 
                     (x + max(label_size[0], dim_size[0]) + 15, y), color, -1)
        cv2.rectangle(vis, (x, y - label_size[1] - 15), 
                     (x + max(label_size[0], dim_size[0]) + 15, y), (255, 255, 255), 2)
        
        # Draw labels
        cv2.putText(vis, label, (x + 8, y - label_size[1] + 5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        cv2.putText(vis, dim_label, (x + 8, y - 5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
        
        # Draw tray index number in center
        center_x, center_y = x + w//2, y + h//2
        cv2.circle(vis, (center_x, center_y), 25, color, -1)
        cv2.circle(vis, (center_x, center_y), 25, (255, 255, 255), 3)
        cv2.putText(vis, str(i+1), (center_x - 10, center_y + 8), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 3)
    
    # Add method indicator
    method_text = f"Tray Detection: {method_name}"
    cv2.rectangle(vis, (10, vis.shape[0] - 35), (300, vis.shape[0] - 5), (50, 50, 50), -1)
    cv2.rectangle(vis, (10, vis.shape[0] - 35), (300, vis.shape[0] - 5), (255, 255, 255), 2)
    cv2.putText(vis, method_text, (15, vis.shape[0] - 15), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

# Monkey-patch the original find_trays function
original_find_trays = find_trays
find_trays = find_trays_enhanced

def main_enhanced():
    """
    Enhanced main function with improved tray detection
    
    This function is identical to the original main() but uses enhanced tray detection
    """
    global roi_selected, drawing, roi_start_x, roi_start_y, roi_end_x, roi_end_y, current_frame
    
    ap = argparse.ArgumentParser()
    ap.add_argument("--source", default="0", help="camera index or media path")
    ap.add_argument("--csv", default=None, help="optional CSV log for counts")
    ap.add_argument("--trip_y", type=float, default=0.85, help="tripline y as fraction of height (default 0.85)")
    ap.add_argument("--enter", type=float, default=12.0, help="tray-blue %% to trigger ENTER")
    ap.add_argument("--exit",  type=float, default=6.0,  help="tray-blue %% to trigger EXIT (hysteresis)")
    ap.add_argument("--max_width", type=int, default=800, help="maximum video width for performance (default 800)")
    ap.add_argument("--save_hsv", default=None, help="save HSV ranges to JSON file")
    ap.add_argument("--load_hsv", default=None, help="load HSV ranges from JSON file")
    ap.add_argument("--event_output", action="store_true", help="emit JSON events for integration")
    ap.add_argument("--line_xmin", type=float, default=0.18, help="tripline left edge as fraction of width (default 0.18)")
    ap.add_argument("--line_xmax", type=float, default=0.82, help="tripline right edge as fraction of width (default 0.82)")
    ap.add_argument("--learn_tray_frames", type=int, default=50, help="frames to learn tray size (default 50)")
    ap.add_argument("--dominant_min_pct", type=float, default=2.0, help="minimum % for dominant SKU (default 2.0)")
    ap.add_argument("--dominant_min_conf", type=float, default=0.5, help="minimum confidence for dominant SKU (default 0.5)")
    
    # Enhanced arguments
    ap.add_argument("--tray_method", default="hybrid", choices=["hybrid", "color_based", "contour_geometry", "edge_hough"],
                   help="Tray detection method (default: hybrid)")
    ap.add_argument("--enhanced_debug", action="store_true", help="Enable enhanced tray detection debugging")
    
    args = ap.parse_args()

    # Load HSV configuration if specified
    if args.load_hsv and os.path.exists(args.load_hsv):
        try:
            with open(args.load_hsv, 'r') as f:
                loaded_ranges = json.load(f)
                RANGES.update(loaded_ranges)
                print(f"✅ Loaded HSV ranges from {args.load_hsv}")
        except Exception as e:
            print(f"❌ Failed to load HSV ranges: {e}")

    # Configure enhanced tray detector
    global enhanced_detector
    config = TrayDetectionConfig(
        debug_mode=args.enhanced_debug,
        min_tray_area_percent=0.04,  # Slightly more sensitive
        max_tray_area_percent=0.45,
        min_aspect_ratio=0.5,
        max_aspect_ratio=2.2
    )
    enhanced_detector = EnhancedTrayDetector(config)
    
    # Store the selected method
    tray_detection_method = args.tray_method

    src = args.source
    if src.isdigit(): src = int(src)
    cap = cv2.VideoCapture(src)
    if not cap.isOpened(): raise SystemExit("Could not open source")

    writer = None
    if args.csv:
        f = open(args.csv, "w", newline="")
        writer = csv.writer(f)
        writer.writerow(["ts","total_crates_passed","dominant_sku","green%","yellow%","red%","blue_sku%","tray_line%","dominant_conf","enhanced_trays","total_pouches"])

    crate_count = 0
    total_crates_passed = 0
    state = "IDLE"
    tray_hist = deque(maxlen=6)
    last_pass_ts = 0
    MIN_GAP_MS = 350
    paused = False
    debug_mode = False
    show_pouch_markers = True
    detection_method = "auto"
    boost_mode = True
    last_frame = None
    bg_model = None

    win = "Enhanced SKU + Crate Counter"
    cv2.namedWindow(win)
    cv2.setMouseCallback(win, mouse_callback)
    
    print("Enhanced Controls:")
    print("  q - Quit")
    print("  s - Save current frame") 
    print("  p - Pause/Resume video")
    print("  d - Toggle debug mode")
    print("  r - Reset crate counter")
    print("  m - Toggle pouch markers")
    print("  t - Toggle detection method")
    print("  z - Select ROI")
    print("  c - Clear ROI selection")
    print("  e - Toggle enhanced tray detection method")
    print("  i - Show detection info")
    print(f"  Enhanced tray detection: {tray_detection_method.upper()}")
    
    frame_skip_counter = 0
    roi_selection_mode = False
    
    while True:
        frame_skip_counter += 1
        
        if boost_mode and frame_skip_counter % 2 != 0 and not paused:
            ret, _ = cap.read()
            if not ret: break
            continue
            
        if not paused:
            ret, frame = cap.read()
            if not ret: break
            last_frame = frame.copy()
        else:
            if last_frame is None: 
                paused = False
                continue
            frame = last_frame.copy()
            ret = True
            
        # Resize frame if too large
        original_h, original_w = frame.shape[:2]
        if original_w > args.max_width:
            scale_factor = args.max_width / original_w
            new_w = args.max_width
            new_h = int(original_h * scale_factor)
            frame = cv2.resize(frame, (new_w, new_h))
            
        h,w = frame.shape[:2]
        current_frame = frame.copy()

        # Tripline ROI
        y0 = int(args.trip_y*h)
        band_h = max(12, h//40)
        y1 = max(0, min(h, y0 + band_h))
        y0 = max(0, min(h-1, y0))
        xL = int(args.line_xmin * w)
        xR = int(args.line_xmax * w)

        # Get masks and SKU percentages
        performance_mode = boost_mode or not debug_mode
        masks = get_masks(frame, performance_mode)
        
        # Apply ROI masking if ROI is selected
        processed_frame, processed_masks = apply_roi_mask(frame, masks)
        
        total = frame.shape[0]*frame.shape[1]
        percs_all = {k: 100.0*np.count_nonzero(v) / total for k,v in processed_masks.items()}
        
        # SKU-only percentages for dominant detection
        sku_keys = ["green", "yellow", "red", "blue_sku"]
        percs = {k: percs_all.get(k, 0.0) for k in sku_keys}
        
        # Dominant SKU calculation
        if any(percs.values()):
            dominant, dom_pct = max(percs.items(), key=lambda kv: kv[1])
            sorted_vals = sorted(percs.values(), reverse=True)
            dominant_conf = sorted_vals[0] - (sorted_vals[1] if len(sorted_vals) > 1 else 0.0)
            
            if dom_pct < args.dominant_min_pct or dominant_conf < args.dominant_min_conf:
                dominant = "none"
        else:
            dominant = "none"
            dom_pct = 0.0
            dominant_conf = 0.0

        # ENHANCED TRAY DETECTION
        try:
            tray_boxes = enhanced_find_trays(processed_frame, processed_masks.get("tray"), 
                                           method=tray_detection_method, max_trays=3)
        except Exception as e:
            print(f"Enhanced tray detection failed: {e}, falling back to original method")
            # Fallback to original method
            tray_learner = TraySizeLearner(n=args.learn_tray_frames, tol=0.25)  # Create if needed
            tray_boxes = original_find_trays(processed_frame, processed_masks["tray"], tray_learner, max_trays=2)
        
        # Calculate tray line coverage
        line_band = (y0, y1, xL, xR)
        line_area = (y1 - y0) * (xR - xL)
        cover = sum(overlap_with_line(box, line_band) for box in tray_boxes)
        tray_pct = 100.0 * cover / max(1, line_area)
        
        # Smooth tray detection
        tray_hist.append(tray_pct)
        tray_pct_smoothed = sum(tray_hist)/len(tray_hist)

        # State machine
        now = cv2.getTickCount() / cv2.getTickFrequency() * 1000
        if state == "IDLE" and tray_pct_smoothed >= args.enter and (now - last_pass_ts) > MIN_GAP_MS:
            state = "PRESENT"
            crate_count += 1
        elif state == "PRESENT" and tray_pct_smoothed <= args.exit:
            state = "IDLE"
            total_crates_passed += 1
            last_pass_ts = now

        # ENHANCED PER-TRAY POUCH DETECTION
        if tray_boxes:
            per_tray_results = enhanced_per_tray_pouch_detection(
                processed_frame, tray_boxes, processed_masks, dominant, detection_method, show_pouch_markers
            )
            
            # Calculate total pouches across all trays
            total_pouches = sum(result[0] for result in per_tray_results.values())
            
            # Main visualization with enhanced tray drawing
            vis = processed_frame.copy()
            draw_enhanced_tray_visualization(vis, tray_boxes, per_tray_results, tray_detection_method.upper())
            
            # Draw per-tray pouches back onto main visualization
            for tray_idx, (tx, ty, tw, th) in enumerate(tray_boxes):
                if tray_idx in per_tray_results:
                    _, tray_vis, pouch_data = per_tray_results[tray_idx]
                    if isinstance(pouch_data, list) and len(pouch_data) > 0:
                        draw_pouches_on(vis, pouch_data, offset=(tx, ty), color=(0,255,255))
        else:
            # No trays detected - run detection on full frame
            if detection_method == "truthful" or (detection_method == "auto" and dominant != "none"):
                pcount, vis, pouch_data = truthful_pouch_count(processed_frame, processed_masks, dominant, show_pouch_markers)
                if pcount < 6 and dominant != "none":
                    pcount, vis, pouch_data = color_pouch_count(processed_frame, processed_masks, dominant, show_pouch_markers)
            elif detection_method == "advanced" or boost_mode:
                pcount, vis, pouch_data = ultra_sensitive_green_detection(processed_frame, processed_masks, show_pouch_markers, debug_mode)[:3]
            else:
                pcount, vis, pouch_data = hybrid_pouch_detection(processed_frame, processed_masks, dominant, show_pouch_markers)
            
            total_pouches = pcount
            per_tray_results = {}

        # Display mode selection - Enhanced UI
        if debug_mode:
            ann = create_debug_display(frame, masks, percs_all, tray_pct_smoothed, state)
        else:
            h, w = frame.shape[:2]
            
            # Draw ROI overlay
            vis_with_roi = draw_roi_on_frame(vis)
            
            # Enhanced canvas with more information
            text_space_top = 140
            text_space_bottom = 100
            text_space_sides = 220
            
            canvas_h = h + text_space_top + text_space_bottom
            canvas_w = w + text_space_sides * 2
            
            canvas = np.zeros((canvas_h, canvas_w, 3), dtype=np.uint8)
            
            # Place video in center
            video_start_y = text_space_top
            video_start_x = text_space_sides
            canvas[video_start_y:video_start_y + h, video_start_x:video_start_x + w] = vis_with_roi
            
            # Enhanced tripline visualization
            tripline_y = video_start_y + y0
            tripline_x_start = video_start_x + xL
            tripline_x_end = video_start_x + xR
            cv2.rectangle(canvas, (tripline_x_start, tripline_y), 
                         (tripline_x_end, tripline_y + band_h), (255,0,255), 2)
            
            # Enhanced information panels
            panels = [
                (10, 10, canvas_w-10, 45),      # Title
                (10, 50, canvas_w-10, 80),      # Detection info
                (10, 85, canvas_w-10, 115),     # Tray info
                (10, 120, canvas_w-10, 135),    # Status
                (10, canvas_h-90, canvas_w-10, canvas_h-60),  # Controls 1
                (10, canvas_h-55, canvas_w-10, canvas_h-25),  # Controls 2
                (10, canvas_h-20, canvas_w-10, canvas_h-5)    # Status bar
            ]
            
            for panel in panels:
                cv2.rectangle(canvas, (panel[0], panel[1]), (panel[2], panel[3]), (40, 40, 40), -1)
                cv2.rectangle(canvas, (panel[0], panel[1]), (panel[2], panel[3]), (200, 200, 200), 1)
            
            # Enhanced text content
            cv2.putText(canvas, f"ENHANCED Tray Detection: {tray_pct_smoothed:.1f}% | State: {state} | Crates in Zone: {crate_count}", 
                        (20, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,255,255), 2)
            
            cv2.putText(canvas, f"Dominant SKU: {dominant} | Green: {percs['green']:.1f}% | Yellow: {percs['yellow']:.1f}% | Red: {percs['red']:.1f}% | Blue: {percs['blue_sku']:.1f}%",
                        (20, 65), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255,255,255), 2)
            
            # Enhanced tray information
            tray_info = f"Method: {tray_detection_method.upper()} | Trays: {len(tray_boxes)} | Total Pouches: {total_pouches if 'total_pouches' in locals() else 0}"
            if per_tray_results:
                per_tray_counts = [f"T{i+1}:{result[0]}" for i, result in per_tray_results.items()]
                if per_tray_counts:
                    tray_info += f" | Per-Tray: {' '.join(per_tray_counts)}"
            
            cv2.putText(canvas, tray_info, (20, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0,255,0), 2)
            
            cv2.putText(canvas, f"TOTAL CRATES PASSED: {total_crates_passed}", 
                        (20, 130), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,0,255), 2)
            
            # Enhanced status and controls
            status_parts = []
            if paused: status_parts.append("PAUSED")
            if debug_mode: status_parts.append("DEBUG")
            if show_pouch_markers: status_parts.append("MARKERS")
            if boost_mode: status_parts.append("BOOST")
            status_parts.append(f"METHOD:{detection_method.upper()}")
            if roi_selected:
                status_parts.append(f"ROI:{roi_end_x - roi_start_x}x{roi_end_y - roi_start_y}")
            
            if status_parts:
                status_text = " | ".join(status_parts)
                cv2.putText(canvas, status_text, (canvas_w-min(len(status_text)*8, 400), 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
            
            cv2.putText(canvas, f"Enhanced Controls: [e] Tray Method | [q] Quit | [s] Save | [p] Pause | [d] Debug | [r] Reset", 
                        (20, canvas_h-75), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
            
            cv2.putText(canvas, f"[m] Markers | [t] Detection | [z] ROI | [c] Clear ROI | [i] Info", 
                        (20, canvas_h-40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
            
            cv2.putText(canvas, f"Enhanced Mode | Video: {w}x{h} | Trays: {len(tray_boxes)} | Detection: {tray_detection_method} | FPS: Processing...", 
                        (20, canvas_h-10), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (150, 150, 150), 1)
            
            ann = canvas

        # Enhanced CSV logging
        if writer:
            ts = datetime.now().isoformat()
            sorted_percs = sorted(percs.values(), reverse=True)
            dominant_conf = sorted_percs[0] - (sorted_percs[1] if len(sorted_percs) > 1 else 0)
            
            enhanced_trays = len(tray_boxes)
            
            writer.writerow([ts, total_crates_passed, dominant, percs["green"], percs["yellow"], 
                           percs["red"], percs["blue_sku"], tray_pct_smoothed, dominant_conf, 
                           enhanced_trays, total_pouches if 'total_pouches' in locals() else 0])
            
            # Enhanced event logging
            save_event("enhanced_frame_analysis", {
                "total_crates_passed": total_crates_passed,
                "dominant_sku": dominant,
                "color_percentages": percs,
                "tray_count": enhanced_trays,
                "total_pouches": total_pouches if 'total_pouches' in locals() else 0,
                "per_tray_counts": {i: result[0] for i, result in per_tray_results.items()} if 'per_tray_results' in locals() else {},
                "detection_method": detection_method,
                "tray_detection_method": tray_detection_method,
                "performance_mode": performance_mode
            })

        cv2.imshow(win, ann)
        key = cv2.waitKey(1) & 0xFF
        
        # Enhanced key controls
        if key == ord('q'): 
            print("🔴 EXITING ENHANCED APPLICATION...")
            break
        elif key == ord('s'):
            ts = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"enhanced_frame_{ts}.png"
            cv2.imwrite(filename, ann)
            print(f"✅ ENHANCED FRAME SAVED: {filename}")
        elif key == ord('p'):
            paused = not paused
            print(f"{'🔴 PAUSED' if paused else '▶️ RESUMED'}")
        elif key == ord('d'):
            debug_mode = not debug_mode
            print(f"🔧 DEBUG MODE: {'ON' if debug_mode else 'OFF'}")
        elif key == ord('r'):
            crate_count = 0
            total_crates_passed = 0
            print("🔄 ENHANCED COUNTERS RESET")
        elif key == ord('m'):
            show_pouch_markers = not show_pouch_markers
            print(f"🔍 POUCH MARKERS: {'ON' if show_pouch_markers else 'OFF'}")
        elif key == ord('t'):
            methods = ["auto", "truthful", "hybrid", "advanced", "traditional"]
            current_idx = methods.index(detection_method)
            detection_method = methods[(current_idx + 1) % len(methods)]
            print(f"⚙️ POUCH DETECTION: {detection_method.upper()}")
        elif key == ord('e'):
            # Cycle through enhanced tray detection methods
            tray_methods = ["hybrid", "color_based", "contour_geometry", "edge_hough"]
            current_idx = tray_methods.index(tray_detection_method)
            tray_detection_method = tray_methods[(current_idx + 1) % len(tray_methods)]
            print(f"🔷 ENHANCED TRAY DETECTION: {tray_detection_method.upper()}")
        elif key == ord('z'):
            roi_selection_mode = True
            print("🎯 ROI SELECTION: Draw rectangle with mouse")
        elif key == ord('c'):
            roi_selected = False
            drawing = False
            roi_selection_mode = False
            print("🔄 ROI CLEARED")
        elif key == ord('i'):
            # Show enhanced detection info
            print(f"\n{'='*50}")
            print("🔷 ENHANCED DETECTION INFO")
            print(f"{'='*50}")
            print(f"Tray Detection Method: {tray_detection_method}")
            print(f"Trays Detected: {len(tray_boxes)}")
            if tray_boxes:
                for i, (x, y, w, h) in enumerate(tray_boxes):
                    print(f"  Tray {i+1}: ({x}, {y}, {w}, {h}) - Area: {w*h:,} pixels")
            if 'per_tray_results' in locals():
                print(f"Per-Tray Pouch Counts:")
                for i, result in per_tray_results.items():
                    print(f"  Tray {i+1}: {result[0]} pouches")
            print(f"Total Pouches: {total_pouches if 'total_pouches' in locals() else 0}")
            print(f"{'='*50}\n")

    cap.release()
    if writer: f.close()
    cv2.destroyAllWindows()
    
    print("✅ Enhanced SKU Crate Counter completed successfully!")

if __name__ == "__main__":
    print("🔷 Enhanced SKU Crate Counter with Robust Tray Detection")
    print("=" * 60)
    print("Key Enhancements:")
    print("✅ Multi-method tray detection (Hybrid, Color-based, Contour-geometry, Edge-Hough)")
    print("✅ Per-tray pouch counting prevents cross-contamination")
    print("✅ Robust tray bounding boxes with enhanced visualization")
    print("✅ Better debugging and monitoring capabilities")
    print("✅ Drop-in compatibility with existing codebase")
    print("=" * 60)
    
    main_enhanced()
