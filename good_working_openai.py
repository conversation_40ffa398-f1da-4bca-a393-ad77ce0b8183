
#!/usr/bin/env python3
"""
SKU Color + Crate Counter (OpenCV)

Additions:
- Never report 'white' as a SKU (excluded from dominant decision)
- Crate counting in video via horizontal ROI tripline on tray-blue percentage with hysteresis
- Rough pouch count inside the largest detected tray using distance-transform peaks
- Visual pouch marking on screen
- Pause/resume functionality
- Debug mode for detailed analysis

Usage:
  python sku_crate_counter.py --source 0
  python sku_crate_counter.py --source myvideo.mp4 --csv counts.csv
Keys: 
  q - quit
  s - save frame
  p - pause/resume video
  d - toggle debug mode (shows color masks and analysis)
  r - reset crate counter
"""

import cv2, numpy as np, argparse, csv
from collections import deque
from datetime import datetime

# HSV ranges
RANGES = {
    "green":  ([40, 60, 60], [85, 255, 255]),
    "yellow": ([20, 80, 80], [35, 255, 255]),
    "pink":   ([140, 60, 60], [175, 255, 255]),
    "blue_b": ([95, 80, 80], [130, 255, 255]),
    "tray":   ([95, 80, 50], [130, 255, 255]),
    "white":  ([0, 0, 180],  [180, 55, 255]),
}

def m(hsv, lo, hi):
    mask = cv2.inRange(hsv, np.array(lo,np.uint8), np.array(hi,np.uint8))
    kernel = np.ones((5,5), np.uint8)
    return cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=1)

def get_masks(bgr):
    hsv = cv2.cvtColor(bgr, cv2.COLOR_BGR2HSV)
    masks = {}
    for k,(lo,hi) in RANGES.items():
        masks[k] = m(hsv, lo, hi)
    masks["blue_sku"] = cv2.bitwise_and(masks["blue_b"], cv2.bitwise_not(masks["tray"]))
    return masks

def draw_contours(dst, mask, color, thick=2):
    cnts,_ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if cnts: cv2.drawContours(dst, cnts, -1, color, thick)

def create_debug_display(frame, masks, percs, tray_pct, state):
    """Create a debug display showing all color masks and analysis."""
    h, w = frame.shape[:2]
    
    # Create larger canvas for debug mode
    text_space_top = 80
    text_space_bottom = 50
    
    # Create a wider layout for debug windows
    debug_h = h // 3
    debug_w = w // 4
    
    canvas_h = h + text_space_top + text_space_bottom
    canvas_w = w * 2 + 100  # Extra space between sections
    
    debug_frame = np.zeros((canvas_h, canvas_w, 3), dtype=np.uint8)
    
    # Original frame on the left with offset
    frame_start_y = text_space_top
    debug_frame[frame_start_y:frame_start_y + h, 50:50 + w] = frame
    
    # Color masks on the right
    colors = {
        'green': (0, 255, 0),
        'yellow': (0, 255, 255), 
        'pink': (255, 0, 255),
        'blue_sku': (255, 0, 0),
        'tray': (255, 255, 0),
        'white': (255, 255, 255)
    }
    
    masks_start_x = w + 100
    positions = [
        (masks_start_x, frame_start_y), (masks_start_x + debug_w, frame_start_y),
        (masks_start_x, frame_start_y + debug_h), (masks_start_x + debug_w, frame_start_y + debug_h),
        (masks_start_x, frame_start_y + debug_h * 2), (masks_start_x + debug_w, frame_start_y + debug_h * 2)
    ]
    
    for i, (mask_name, color) in enumerate(colors.items()):
        if i < len(positions):
            x, y = positions[i]
            # Resize mask to fit debug window
            mask_resized = cv2.resize(masks[mask_name], (debug_w, debug_h))
            colored_mask = cv2.cvtColor(mask_resized, cv2.COLOR_GRAY2BGR)
            colored_mask = np.where(colored_mask > 0, color, [50, 50, 50])
            debug_frame[y:y+debug_h, x:x+debug_w] = colored_mask
            
            # Add label with background
            cv2.rectangle(debug_frame, (x, y-25), (x + debug_w, y), (0, 0, 0), -1)
            cv2.putText(debug_frame, f"{mask_name}: {percs.get(mask_name, 0):.1f}%", 
                       (x + 5, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    
    # Top information bar
    cv2.rectangle(debug_frame, (10, 10), (canvas_w-10, 40), (50, 50, 50), -1)
    cv2.rectangle(debug_frame, (10, 10), (canvas_w-10, 40), (255, 255, 255), 1)
    
    cv2.rectangle(debug_frame, (10, 45), (canvas_w-10, 75), (50, 50, 50), -1)
    cv2.rectangle(debug_frame, (10, 45), (canvas_w-10, 75), (255, 255, 255), 1)
    
    cv2.putText(debug_frame, f"DEBUG MODE - Tray Detection: {tray_pct:.1f}% | State: {state}", 
               (20, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
    
    cv2.putText(debug_frame, f"Original Video (Left) | Color Masks Analysis (Right)", 
               (20, 65), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # Bottom information
    cv2.rectangle(debug_frame, (10, canvas_h-40), (canvas_w-10, canvas_h-10), (50, 50, 50), -1)
    cv2.rectangle(debug_frame, (10, canvas_h-40), (canvas_w-10, canvas_h-10), (255, 255, 255), 1)
    
    cv2.putText(debug_frame, f"Press 'd' to exit debug mode | Press 'q' to quit", 
               (20, canvas_h-20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
    
    return debug_frame

def approx_pouch_count(bgr, tray_mask, show_markers=False):
    """Find largest tray region and count white 'pouch centers' via distance peaks."""
    cnts,_ = cv2.findContours(tray_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if not cnts: return 0, bgr, []
    c = max(cnts, key=cv2.contourArea)
    x,y,w,h = cv2.boundingRect(c)
    crop = bgr[y:y+h, x:x+w]
    hsv = cv2.cvtColor(crop, cv2.COLOR_BGR2HSV)
    white = m(hsv, RANGES["white"][0], RANGES["white"][1])
    tray  = m(hsv, RANGES["tray"][0],  RANGES["tray"][1])
    white = cv2.bitwise_and(white, cv2.bitwise_not(tray))
    white = cv2.morphologyEx(white, cv2.MORPH_CLOSE, np.ones((7,7),np.uint8), iterations=1)
    dist = cv2.distanceTransform(white, cv2.DIST_L2, 3)
    if dist.max() <= 0: return 0, bgr, []
    peaks = (dist > (0.35*dist.max())).astype(np.uint8)*255
    peaks = cv2.morphologyEx(peaks, cv2.MORPH_OPEN, np.ones((9,9),np.uint8), iterations=1)
    
    # Find pouch centers
    pouch_centers = []
    cnts_peaks,_ = cv2.findContours(peaks, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for cnt in cnts_peaks:
        M = cv2.moments(cnt)
        if M["m00"] > 0:
            cx = int(M["m10"] / M["m00"]) + x
            cy = int(M["m01"] / M["m00"]) + y
            pouch_centers.append((cx, cy))
    
    count = len(pouch_centers)
    vis = bgr.copy()
    cv2.rectangle(vis, (x,y), (x+w,y+h), (255,0,0), 2)
    
    # Mark pouches if requested
    if show_markers:
        for i, (cx, cy) in enumerate(pouch_centers):
            cv2.circle(vis, (cx, cy), 8, (0, 255, 0), 2)  # Green circles
            cv2.putText(vis, str(i+1), (cx-5, cy+5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    return count, vis, pouch_centers

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--source", default="0", help="camera index or media path")
    ap.add_argument("--csv", default=None, help="optional CSV log for counts")
    ap.add_argument("--trip_y", type=float, default=0.85, help="tripline y as fraction of height (default 0.85)")
    ap.add_argument("--enter", type=float, default=12.0, help="tray-blue %% to trigger ENTER")
    ap.add_argument("--exit",  type=float, default=6.0,  help="tray-blue %% to trigger EXIT (hysteresis)")
    ap.add_argument("--max_width", type=int, default=800, help="maximum video width for performance (default 800)")
    args = ap.parse_args()

    src = args.source
    if src.isdigit(): src = int(src)
    cap = cv2.VideoCapture(src)
    if not cap.isOpened(): raise SystemExit("Could not open source")

    writer = None
    if args.csv:
        f = open(args.csv, "w", newline="")
        writer = csv.writer(f)
        writer.writerow(["ts","crate_count","dominant_sku","green%","yellow%","pink%","blue_sku%"])

    crate_count = 0
    total_crates_passed = 0  # Track total crates that have completely passed
    state = "IDLE"  # IDLE -> PRESENT
    history = deque(maxlen=10)
    paused = False
    debug_mode = False
    show_pouch_markers = True

    win = "SKU + Crate Counter"
    print("Controls:")
    print("  q - Quit")
    print("  s - Save current frame")
    print("  p - Pause/Resume video")
    print("  d - Toggle debug mode")
    print("  r - Reset crate counter")
    print("  m - Toggle pouch markers")
    
    while True:
        if not paused:
            ret, frame = cap.read()
            if not ret: break
        else:
            # When paused, use the last frame
            ret = True
            
        # Resize frame if too large for better performance and display
        original_h, original_w = frame.shape[:2]
        if original_w > args.max_width:
            scale_factor = args.max_width / original_w
            new_w = args.max_width
            new_h = int(original_h * scale_factor)
            frame = cv2.resize(frame, (new_w, new_h))
            
        h,w = frame.shape[:2]

        # Tripline ROI
        y0 = int(args.trip_y*h)
        band_h = max(12, h//40)
        roi_line = frame[y0:y0+band_h, :]
        masks_line = get_masks(roi_line)
        tray_pct = 100.0 * np.count_nonzero(masks_line["tray"]) / (roi_line.shape[0]*roi_line.shape[1])

        # State machine
        if state == "IDLE" and tray_pct >= args.enter:
            state = "PRESENT"
            crate_count += 1
        elif state == "PRESENT" and tray_pct <= args.exit:
            state = "IDLE"
            total_crates_passed += 1  # A crate has completely passed

        # Full-frame SKU percentages (never treating white as SKU)
        masks = get_masks(frame)
        total = frame.shape[0]*frame.shape[1]
        percs = {k: 100.0*np.count_nonzero(masks[k]) / total for k in ["green","yellow","pink","blue_sku"]}
        dominant = max(percs.items(), key=lambda kv: kv[1])[0] if any(percs.values()) else "none"

        # Rough pouch count (optional visualization)
        pcount, vis, pouch_centers = approx_pouch_count(frame, masks["tray"], show_pouch_markers)

        # Display mode selection
        if debug_mode:
            # Show debug display with all color masks
            ann = create_debug_display(frame, masks, percs, tray_pct, state)
        else:
            # Normal display mode - create expanded canvas
            h, w = frame.shape[:2]
            
            # Create larger canvas with extra space for text
            text_space_top = 120  # Space at top for text
            text_space_bottom = 80  # Space at bottom for text
            text_space_sides = 200  # Space on sides for text
            
            canvas_h = h + text_space_top + text_space_bottom
            canvas_w = w + text_space_sides * 2
            
            # Create black canvas
            canvas = np.zeros((canvas_h, canvas_w, 3), dtype=np.uint8)
            
            # Place video in center of canvas
            video_start_y = text_space_top
            video_start_x = text_space_sides
            canvas[video_start_y:video_start_y + h, video_start_x:video_start_x + w] = vis
            
            # Draw tripline on the video area
            tripline_y = video_start_y + y0
            cv2.rectangle(canvas, (video_start_x, tripline_y), 
                         (video_start_x + w, tripline_y + band_h), (255,0,255), 2)
            
            # Top text area - clear backgrounds
            cv2.rectangle(canvas, (10, 10), (canvas_w-10, 40), (50, 50, 50), -1)
            cv2.rectangle(canvas, (10, 10), (canvas_w-10, 40), (255, 255, 255), 1)
            
            cv2.rectangle(canvas, (10, 45), (canvas_w-10, 75), (50, 50, 50), -1)
            cv2.rectangle(canvas, (10, 45), (canvas_w-10, 75), (255, 255, 255), 1)
            
            cv2.rectangle(canvas, (10, 80), (canvas_w-10, 110), (50, 50, 50), -1)
            cv2.rectangle(canvas, (10, 80), (canvas_w-10, 110), (255, 255, 255), 1)
            
            # Bottom text area
            cv2.rectangle(canvas, (10, canvas_h-70), (canvas_w-10, canvas_h-40), (50, 50, 50), -1)
            cv2.rectangle(canvas, (10, canvas_h-70), (canvas_w-10, canvas_h-40), (255, 255, 255), 1)
            
            cv2.rectangle(canvas, (10, canvas_h-35), (canvas_w-10, canvas_h-5), (50, 50, 50), -1)
            cv2.rectangle(canvas, (10, canvas_h-35), (canvas_w-10, canvas_h-5), (255, 255, 255), 1)
            
            # Status indicators on the right side
            status_text = []
            if paused: status_text.append("PAUSED")
            if debug_mode: status_text.append("DEBUG")
            if show_pouch_markers: status_text.append("MARKERS ON")
            if status_text:
                cv2.rectangle(canvas, (canvas_w-180, 10), (canvas_w-10, 40), (0, 100, 0), -1)
                cv2.rectangle(canvas, (canvas_w-180, 10), (canvas_w-10, 40), (255, 255, 255), 1)
                cv2.putText(canvas, " | ".join(status_text), (canvas_w-175, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1, cv2.LINE_AA)
            
            # Main information text - well spaced and clearly visible
            cv2.putText(canvas, f"Tray Detection: {tray_pct:.1f}% | State: {state} | Current Crates in Zone: {crate_count}", 
                        (20, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,0,255), 2, cv2.LINE_AA)
            
            cv2.putText(canvas, f"Dominant SKU: {dominant} | Green: {percs['green']:.1f}% | Yellow: {percs['yellow']:.1f}% | Pink: {percs['pink']:.1f}% | Blue: {percs['blue_sku']:.1f}%",
                        (20, 65), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,0,255), 2, cv2.LINE_AA)
            
            cv2.putText(canvas, f"TOTAL CRATES PASSED: {total_crates_passed} | Pouches in Current Tray: {pcount}", 
                        (20, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,255,0), 2, cv2.LINE_AA)
            
            # Bottom information
            cv2.putText(canvas, f"Controls: [q]Quit [s]Save [p]Pause [d]Debug [r]Reset [m]Markers", 
                        (20, canvas_h-50), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255,255,0), 2, cv2.LINE_AA)
            
            cv2.putText(canvas, f"Video Resolution: {w}x{h} | Canvas: {canvas_w}x{canvas_h}", 
                        (20, canvas_h-20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200,200,200), 1, cv2.LINE_AA)
            
            ann = canvas

        if writer:
            ts = datetime.now().isoformat()
            writer.writerow([ts, crate_count, dominant, percs["green"], percs["yellow"], percs["pink"], percs["blue_sku"]])

        cv2.imshow(win, ann)
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'): 
            break
        elif key == ord('s'):
            ts = datetime.now().strftime("%Y%m%d_%H%M%S")
            cv2.imwrite(f"frame_{ts}.png", ann)
            print(f"Frame saved as frame_{ts}.png")
        elif key == ord('p'):
            paused = not paused
            print(f"Video {'paused' if paused else 'resumed'}")
        elif key == ord('d'):
            debug_mode = not debug_mode
            print(f"Debug mode {'enabled' if debug_mode else 'disabled'}")
        elif key == ord('r'):
            crate_count = 0
            total_crates_passed = 0
            print("Crate counters reset")
        elif key == ord('m'):
            show_pouch_markers = not show_pouch_markers
            print(f"Pouch markers {'enabled' if show_pouch_markers else 'disabled'}")

    cap.release()
    if writer: f.close()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()
