#!/usr/bin/env python3
"""
Config Demonstration Script
===========================

This script shows how to modify configuration parameters in config.py
and see the effects on the crate recognition system.

Example usage:
1. Edit config.py values
2. Run this script to see current settings
3. Run v1_optimized.py to see changes in action
"""

from config import *
import sys

def show_config_summary():
    """Display current configuration summary"""
    print("=" * 70)
    print("CRATE RECOGNITION CV - CURRENT CONFIGURATION")
    print("=" * 70)
    
    print("\n🎨 COLOR DETECTION SETTINGS:")
    print(f"  • White Gate Max Saturation: {WHITE_S_MAX}")
    print(f"  • White Gate Min Value: {WHITE_V_MIN}")
    print(f"  • Tray Rim Margin: {TRAY_RIM_MARGIN} px")
    print(f"  • White Gate Expansion: {WHITE_GATE_EXPANSION_FACTOR * 100}% of tray size")
    print(f"  • Available Colors: {list(COLOR_RANGES.keys())}")
    
    print(f"\n📐 GEOMETRIC PARAMETERS:")
    print(f"  • ROI Default Margin: {ROI_MARGIN_DEFAULT} px")
    print(f"  • Min Pouch Area: {MIN_POUCH_AREA_FRACTION * 100}% of tray ({MIN_POUCH_AREA_ABSOLUTE} px min)")
    print(f"  • Max Pouch Area: {MAX_POUCH_AREA_FRACTION * 100}% of tray")
    print(f"  • Color Kernel Size: {COLOR_KERNEL_SIZE}x{COLOR_KERNEL_SIZE}")
    
    print(f"\n⚡ PERFORMANCE SETTINGS:")
    print(f"  • Target FPS: {TARGET_FPS_DEFAULT}")
    print(f"  • Skip Frames: {SKIP_FRAMES_DEFAULT}")
    print(f"  • CLAHE Clip Limit: {CLAHE_CLIP_LIMIT}")
    print(f"  • CLAHE Grid Size: {CLAHE_TILE_GRID_SIZE}")
    
    print(f"\n🖥️  UI/DISPLAY SETTINGS:")
    print(f"  • Panel Width: {PANEL_WIDTH} px")
    print(f"  • Panel Background: {PANEL_BACKGROUND_COLOR}")
    print(f"  • Line Height: {LINE_HEIGHT} px")
    print(f"  • Corner Marker Size: {CORNER_MARKER_SIZE} px")
    print(f"  • Available Tray Colors: {len(TRAY_BOUNDARY_COLORS)} colors")
    
    print(f"\n📁 FILE OUTPUT:")
    print(f"  • Output Directory: {OUTPUT_DIR_DEFAULT}/")
    print(f"  • Clean Crops: {TRAY_CROPS_CLEAN_SUBDIR}/")
    print(f"  • Annotated Crops: {TRAY_CROPS_ANNOTATED_SUBDIR}/")

def show_tuning_tips():
    """Display tuning recommendations"""
    print("\n" + "=" * 70)
    print("TUNING TIPS")
    print("=" * 70)
    
    print("\n🔧 Common Adjustments:")
    print("  • Missing pouches? Increase WHITE_S_MAX or decrease WHITE_V_MIN")
    print("  • Too many false positives? Decrease WHITE_S_MAX or increase WHITE_V_MIN")
    print("  • Edge detection issues? Adjust TRAY_RIM_MARGIN")
    print("  • Wrong pouch sizes? Modify MIN/MAX_POUCH_AREA_FRACTION")
    print("  • Performance issues? Increase SKIP_FRAMES_DEFAULT")
    print("  • UI too small/large? Adjust PANEL_WIDTH and font scales")
    
    print("\n📝 To make changes:")
    print("  1. Edit values in config.py")
    print("  2. Save the file") 
    print("  3. Run v1_optimized.py to see effects")
    print("  4. No need to restart - config is loaded fresh each time")

def validate_config():
    """Validate configuration values are reasonable"""
    issues = []
    
    if WHITE_S_MAX > 255 or WHITE_S_MAX < 0:
        issues.append(f"WHITE_S_MAX ({WHITE_S_MAX}) should be 0-255")
    
    if WHITE_V_MIN > 255 or WHITE_V_MIN < 0:
        issues.append(f"WHITE_V_MIN ({WHITE_V_MIN}) should be 0-255")
        
    if MIN_POUCH_AREA_FRACTION > MAX_POUCH_AREA_FRACTION:
        issues.append("MIN_POUCH_AREA_FRACTION cannot be > MAX_POUCH_AREA_FRACTION")
        
    if TARGET_FPS_DEFAULT <= 0:
        issues.append("TARGET_FPS_DEFAULT must be positive")
        
    if SKIP_FRAMES_DEFAULT < 1:
        issues.append("SKIP_FRAMES_DEFAULT must be >= 1")
    
    if issues:
        print("\n⚠️  CONFIGURATION ISSUES:")
        for issue in issues:
            print(f"  • {issue}")
    else:
        print("\n✅ Configuration validation passed!")

if __name__ == "__main__":
    show_config_summary()
    validate_config()
    show_tuning_tips()
    
    print("\n" + "=" * 70)
    print(f"To run the system: python v1_optimized.py --input ./videos/2.mp4")
    print("=" * 70)
