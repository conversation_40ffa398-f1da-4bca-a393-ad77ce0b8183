
#!/usr/bin/env python3
"""
SKU Color Detector for Dairy Pouches (OpenCV)

Features
- Works with image, video file, or live webcam (default 0)
- Detects SKU colors (green, yellow, pink/magenta, blue) in blue trays
- Excludes tray-blue from blue SKU detections
- Crops a small border to avoid tray walls
- Overlays contours and running percentage per color
- Optional CSV logging
- Optional basic calibration sliders for two ranges (tray-blue, green) to quickly tune on site

Usage
  python sku_color_detector.py --source myvideo.mp4
  python sku_color_detector.py --source myimage.jpg
  python sku_color_detector.py                      # webcam 0
  python sku_color_detector.py --source 0 --calibrate

Keys
  q : quit
  s : save current annotated frame to 'frame_YYYYmmdd_HHMMSS.png'
"""

import cv2
import numpy as np
import argparse
import time
import os
from collections import deque
from datetime import datetime

# -------------------- Default HSV ranges (tweak on site) --------------------
DEFAULT_RANGES = {
    # SKU colors
    "green":  ([40,  60,  60], [85,  255, 255]),
    "yellow": ([20,  80,  80], [35,  255, 255]),
    "pink":   ([140, 60,  60], [175, 255, 255]),
    # blue_sku will be computed but tray-blue is excluded
    "blue_sku_base": ([95, 80, 80], [130, 255, 255]),
    # Tray color to exclude
    "tray_blue": ([95, 80, 50], [130, 255, 255]),
    # White pouch base (low saturation, high value) - optional info
    "white":  ([0,   0,  180], [180, 50, 255]),
}

# -------------------- Helpers --------------------
def np8(x): return np.array(x, dtype=np.uint8)

def build_mask(hsv, lo, hi):
    return cv2.inRange(hsv, np8(lo), np8(hi))

def clean_mask(mask, k=5):
    kernel = np.ones((k, k), np.uint8)
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=1)
    return mask

def draw_contours(dst_bgr, mask, color_bgr, thickness=2):
    cnts, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if cnts:
        cv2.drawContours(dst_bgr, cnts, -1, color_bgr, thickness)

def percent(n, total): 
    return (float(n) / float(total) * 100.0) if total > 0 else 0.0

def put_text_multi(img, lines, org=(10, 22), scale=0.7):
    x, y = org
    for line in lines:
        cv2.putText(img, line, (x, y), cv2.FONT_HERSHEY_SIMPLEX, scale, (0,0,0), 2, cv2.LINE_AA)
        cv2.putText(img, line, (x, y), cv2.FONT_HERSHEY_SIMPLEX, scale, (255,255,255), 1, cv2.LINE_AA)
        y += int(28*scale)

# -------------------- Processing --------------------
def process_frame(frame_bgr, ranges, border_frac=0.05, rssi_dummy=None):
    """Return annotated frame, percentages dict"""
    h, w = frame_bgr.shape[:2]
    b = int(border_frac * min(h, w))
    roi = frame_bgr[b:h-b, b:w-b]
    hsv = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)

    masks = {}
    for k, (lo, hi) in ranges.items():
        masks[k] = clean_mask(build_mask(hsv, lo, hi))

    # blue_sku := blue_sku_base minus tray_blue
    blue_base = masks.get("blue_sku_base", None)
    tray_blue = masks.get("tray_blue", None)
    blue_sku = None
    if blue_base is not None and tray_blue is not None:
        inv_tray = cv2.bitwise_not(tray_blue)
        blue_sku = cv2.bitwise_and(blue_base, inv_tray)

    total = roi.shape[0]*roi.shape[1]
    counts = {}
    counts["green"]   = int(np.count_nonzero(masks["green"]))
    counts["yellow"]  = int(np.count_nonzero(masks["yellow"]))
    counts["pink"]    = int(np.count_nonzero(masks["pink"]))
    counts["blue_sku"]= int(np.count_nonzero(blue_sku)) if blue_sku is not None else 0
    counts["white"]   = int(np.count_nonzero(masks["white"]))
    counts["tray_blue"]=int(np.count_nonzero(masks["tray_blue"]))
    counts["other"]   = int(total - sum(counts.values()))

    # Visualization
    overlay = roi.copy()
    draw_contours(overlay, masks["green"],    (0,255,0))
    draw_contours(overlay, masks["yellow"],   (0,255,255))
    draw_contours(overlay, masks["pink"],     (255,0,255))
    if blue_sku is not None:
        draw_contours(overlay, blue_sku,      (0,128,255))  # orange for distinction

    # Compose back into original frame
    out = frame_bgr.copy()
    out[b:h-b, b:w-b] = overlay

    # Percentages
    percs = {k: percent(v, total) for k, v in counts.items()}
    # Dominant SKU color (ignore tray & white & other)
    sku_only = {k:v for k,v in percs.items() if k in ["green","yellow","pink","blue_sku"]}
    dominant = max(sku_only.items(), key=lambda kv: kv[1])[0] if sku_only else "none"

    lines = [
        f"Green:{percs['green']:.1f}%  Yellow:{percs['yellow']:.1f}%  Pink:{percs['pink']:.1f}%  BlueSKU:{percs['blue_sku']:.1f}%",
        f"TrayBlue:{percs['tray_blue']:.1f}%  White:{percs['white']:.1f}%  Dominant SKU:{dominant}"
    ]
    put_text_multi(out, lines, org=(12, 26), scale=0.7)
    return out, percs

# -------------------- Calibration UI (basic) --------------------
def calibrate_loop(source, ranges):
    def nothing(x): pass
    win = "calibrate"
    cv2.namedWindow(win)
    # We'll let user adjust tray_blue H/S/V low/high and green H/S/V low/high quickly
    sliders = {
        "tray_lo_H": [ranges["tray_blue"][0][0], 179],
        "tray_lo_S": [ranges["tray_blue"][0][1], 255],
        "tray_lo_V": [ranges["tray_blue"][0][2], 255],
        "tray_hi_H": [ranges["tray_blue"][1][0], 179],
        "tray_hi_S": [ranges["tray_blue"][1][1], 255],
        "tray_hi_V": [ranges["tray_blue"][1][2], 255],
        "gr_lo_H": [ranges["green"][0][0], 179],
        "gr_lo_S": [ranges["green"][0][1], 255],
        "gr_lo_V": [ranges["green"][0][2], 255],
        "gr_hi_H": [ranges["green"][1][0], 179],
        "gr_hi_S": [ranges["green"][1][1], 255],
        "gr_hi_V": [ranges["green"][1][2], 255],
    }
    for k,(val,maxv) in sliders.items():
        cv2.createTrackbar(k, win, int(val), maxv, nothing)

    cap = cv2.VideoCapture(source)
    if not cap.isOpened():
        print("Unable to open source for calibration:", source)
        return ranges

    while True:
        ret, frame = cap.read()
        if not ret:
            break
        # Update ranges live
        ranges["tray_blue"] = ([cv2.getTrackbarPos("tray_lo_H", win),
                                cv2.getTrackbarPos("tray_lo_S", win),
                                cv2.getTrackbarPos("tray_lo_V", win)],
                               [cv2.getTrackbarPos("tray_hi_H", win),
                                cv2.getTrackbarPos("tray_hi_S", win),
                                cv2.getTrackbarPos("tray_hi_V", win)])
        ranges["green"] = ([cv2.getTrackbarPos("gr_lo_H", win),
                            cv2.getTrackbarPos("gr_lo_S", win),
                            cv2.getTrackbarPos("gr_lo_V", win)],
                           [cv2.getTrackbarPos("gr_hi_H", win),
                            cv2.getTrackbarPos("gr_hi_S", win),
                            cv2.getTrackbarPos("gr_hi_V", win)])
        annotated, _ = process_frame(frame, ranges)
        cv2.imshow(win, annotated)
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break

    cap.release()
    cv2.destroyWindow(win)
    return ranges

# -------------------- Main --------------------
def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--source", default="0",
                    help="path to image/video or camera index (default 0)")
    ap.add_argument("--border", type=float, default=0.05, help="border crop fraction (default 0.05)")
    ap.add_argument("--csv", default=None, help="optional csv log file path")
    ap.add_argument("--calibrate", action="store_true", help="open quick calibration sliders for tray-blue & green")
    args = ap.parse_args()

    # Prepare ranges (deep copy)
    ranges = {k:(list(v[0]), list(v[1])) for k,v in DEFAULT_RANGES.items()}

    # Parse source
    source = args.source
    if source.isdigit():
        source = int(source)

    # Calibration (for video/webcam only)
    if args.calibrate and not isinstance(source, str):
        ranges = calibrate_loop(source, ranges)

    # Try to determine if it's an image
    is_image = False
    if isinstance(source, str):
        lower = source.lower()
        is_image = any(lower.endswith(ext) for ext in [".jpg",".jpeg",".png",".bmp",".webp"])

    if is_image:
        frame = cv2.imread(source)
        if frame is None:
            raise SystemExit(f"Could not read image: {source}")
        annotated, percs = process_frame(frame, ranges, border_frac=args.border)
        print("Percentages:", percs)
        # Show
        win = "SKU Color Detector"
        cv2.imshow(win, annotated)
        print("Press 'q' to quit, 's' to save annotated image.")
        while True:
            key = cv2.waitKey(0) & 0xFF
            if key == ord('q'):
                break
            if key == ord('s'):
                ts = datetime.now().strftime("%Y%m%d_%H%M%S")
                outp = f"frame_{ts}.png"
                cv2.imwrite(outp, annotated)
                print("Saved:", outp)
        cv2.destroyAllWindows()
    else:
        cap = cv2.VideoCapture(source)
        if not cap.isOpened():
            raise SystemExit(f"Could not open source: {source}")
        # CSV
        if args.csv:
            import csv
            csv_f = open(args.csv, "w", newline="")
            writer = csv.writer(csv_f)
            writer.writerow(["ts","green%","yellow%","pink%","blue_sku%","white%","tray_blue%","other%","dominant"])
        else:
            writer = None

        # Smoothing
        smooth = {k: deque(maxlen=10) for k in ["green","yellow","pink","blue_sku","white","tray_blue","other"]}

        win = "SKU Color Detector"
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            annotated, percs = process_frame(frame, ranges, border_frac=args.border)
            for k in smooth:
                smooth[k].append(percs[k])

            # Show smoothed dominant
            sku_keys = ["green","yellow","pink","blue_sku"]
            avg = {k: (sum(smooth[k])/len(smooth[k]) if smooth[k] else 0.0) for k in smooth}
            dominant = max({k:avg[k] for k in sku_keys}.items(), key=lambda kv: kv[1])[0] if any(avg[k] for k in sku_keys) else "none"
            put_text_multi(annotated, [f"Smoothed Dominant SKU: {dominant}"], org=(12, 60), scale=0.7)

            if writer:
                ts = datetime.now().isoformat()
                writer.writerow([ts, avg["green"], avg["yellow"], avg["pink"], avg["blue_sku"], avg["white"], avg["tray_blue"], avg["other"], dominant])

            cv2.imshow(win, annotated)
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            if key == ord('s'):
                ts = datetime.now().strftime("%Y%m%d_%H%M%S")
                outp = f"frame_{ts}.png"
                cv2.imwrite(outp, annotated)
                print("Saved:", outp)

        cap.release()
        if args.csv:
            csv_f.close()
        cv2.destroyAllWindows()

if __name__ == "__main__":
    main()
