
#!/usr/bin/env python3
"""
SKU Color + Crate Counter (OpenCV)

Additions:
- Never report 'white' as a SKU (excluded from dominant decision)
- Crate counting in video via horizontal ROI tripline on tray-blue percentage with hysteresis
- Rough pouch count inside the largest detected tray using distance-transform peaks
- Visual pouch marking on screen
- Pause/resume functionality
- Debug mode for detailed analysis

Usage:
  python sku_crate_counter.py --source 0
  python sku_crate_counter.py --source myvideo.mp4 --csv counts.csv
Keys: 
  q - quit
  s - save frame
  p - pause/resume video
  d - toggle debug mode (shows color masks and analysis)
  r - reset crate counter
"""

import cv2, numpy as np, argparse, csv, datetime, json, os
from collections import deque

# Global variables for ROI selection
drawing = False
roi_selected = False
roi_start_x, roi_start_y = -1, -1
roi_end_x, roi_end_y = -1, -1
current_frame = None

def watershed_pouch_separation(mask, min_distance=20):
    """Advanced pouch separation using watershed segmentation with OpenCV fallback."""
    try:
        # Import here to handle optional dependencies gracefully
        from scipy import ndimage
        from skimage.segmentation import watershed
        from skimage.feature import peak_local_maxima
        
        # Distance transform
        dist_transform = cv2.distanceTransform(mask, cv2.DIST_L2, 5)
        
        # Find local maxima as markers
        local_maxima = peak_local_maxima(dist_transform, min_distance=min_distance, threshold_abs=0.3*dist_transform.max())
        markers = np.zeros(dist_transform.shape, dtype=np.int32)
        
        for i, (y, x) in enumerate(local_maxima):
            markers[y, x] = i + 1
            
        if len(local_maxima) == 0:
            return opencv_watershed_fallback(mask)
        
        # Apply watershed
        labels = watershed(-dist_transform, markers, mask=mask)
        
        # Extract individual pouches
        separated_pouches = []
        for label in np.unique(labels):
            if label == 0:  # Skip background
                continue
            pouch_mask = (labels == label).astype(np.uint8) * 255
            separated_pouches.append(pouch_mask)
            
        return labels, separated_pouches
        
    except ImportError:
        print("📦 Using OpenCV watershed (scipy/scikit-image not available)")
        return opencv_watershed_fallback(mask)
    except Exception as e:
        print(f"⚠️ Watershed separation failed: {e}")
        return opencv_watershed_fallback(mask)

def opencv_watershed_fallback(mask):
    """OpenCV-only watershed implementation."""
    # Distance transform
    dist_transform = cv2.distanceTransform(mask, cv2.DIST_L2, 5)
    
    # Find local maxima using morphological operations
    kernel = np.ones((5,5), np.uint8)
    local_maxima = cv2.dilate(dist_transform, kernel)
    local_maxima = (local_maxima == dist_transform) & (dist_transform > 0.7 * dist_transform.max())
    
    # Create markers
    markers = np.zeros(mask.shape, dtype=np.int32)
    markers[local_maxima] = np.arange(1, np.count_nonzero(local_maxima) + 1)
    
    if np.max(markers) == 0:
        return mask, [mask]
    
    # Convert mask to 3-channel for watershed
    mask_3ch = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR)
    
    # Apply watershed
    cv2.watershed(mask_3ch, markers)
    
    # Extract separated pouches
    separated_pouches = []
    for label in range(1, np.max(markers) + 1):
        pouch_mask = ((markers == label) * 255).astype(np.uint8)
        if np.count_nonzero(pouch_mask) > 0:
            separated_pouches.append(pouch_mask)
    
    return markers, separated_pouches

def save_event(event_type, data, event_file="events.json"):
    """Save structured events for integration with other systems."""
    event = {
        "timestamp": datetime.now().isoformat(),
        "event_type": event_type,
        "data": data
    }
    
    # Append to events file
    events_list = []
    if os.path.exists(event_file):
        try:
            with open(event_file, 'r') as f:
                events_list = json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            events_list = []
    
    events_list.append(event)
    
    # Keep only last 1000 events to prevent file bloat
    if len(events_list) > 1000:
        events_list = events_list[-1000:]
    
    with open(event_file, 'w') as f:
        json.dump(events_list, f, indent=2)

def mouse_callback(event, x, y, flags, param):
    """Mouse callback for ROI selection."""
    global drawing, roi_start_x, roi_start_y, roi_end_x, roi_end_y, roi_selected, current_frame
    
    if event == cv2.EVENT_LBUTTONDOWN:
        drawing = True
        roi_start_x, roi_start_y = x, y
        roi_end_x, roi_end_y = x, y
        roi_selected = False
        
    elif event == cv2.EVENT_MOUSEMOVE:
        if drawing:
            roi_end_x, roi_end_y = x, y
            
    elif event == cv2.EVENT_LBUTTONUP:
        drawing = False
        roi_end_x, roi_end_y = x, y
        if abs(roi_end_x - roi_start_x) > 10 and abs(roi_end_y - roi_start_y) > 10:
            roi_selected = True
            # Ensure proper ordering (top-left to bottom-right)
            x1, y1 = min(roi_start_x, roi_end_x), min(roi_start_y, roi_end_y)
            x2, y2 = max(roi_start_x, roi_end_x), max(roi_start_y, roi_end_y)
            roi_start_x, roi_start_y, roi_end_x, roi_end_y = x1, y1, x2, y2
            print(f"✅ ROI SELECTED: ({roi_start_x}, {roi_start_y}) to ({roi_end_x}, {roi_end_y})")
            print(f"📏 ROI SIZE: {roi_end_x - roi_start_x} x {roi_end_y - roi_start_y}")

def draw_roi_on_frame(frame):
    """Draw the current ROI selection on the frame."""
    global drawing, roi_start_x, roi_start_y, roi_end_x, roi_end_y, roi_selected
    
    display_frame = frame.copy()
    
    if roi_selected:
        # Draw selected ROI with thick green rectangle
        cv2.rectangle(display_frame, (roi_start_x, roi_start_y), (roi_end_x, roi_end_y), (0, 255, 0), 3)
        # Add ROI label
        cv2.putText(display_frame, "ROI ACTIVE", (roi_start_x, roi_start_y - 10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        # Add semi-transparent overlay to show ROI clearly
        overlay = display_frame.copy()
        cv2.rectangle(overlay, (roi_start_x, roi_start_y), (roi_end_x, roi_end_y), (0, 255, 0), -1)
        cv2.addWeighted(display_frame, 0.95, overlay, 0.05, 0, display_frame)
        
    elif drawing:
        # Draw current selection rectangle while dragging
        cv2.rectangle(display_frame, (roi_start_x, roi_start_y), (roi_end_x, roi_end_y), (0, 255, 255), 2)
        cv2.putText(display_frame, "SELECTING ROI...", (roi_start_x, roi_start_y - 10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
    
    return display_frame

def apply_roi_mask(frame, masks):
    """Apply ROI mask to frame and all color masks."""
    global roi_selected, roi_start_x, roi_start_y, roi_end_x, roi_end_y
    
    if not roi_selected:
        return frame, masks
    
    h, w = frame.shape[:2]
    
    # Create ROI mask
    roi_mask = np.zeros((h, w), dtype=np.uint8)
    roi_mask[roi_start_y:roi_end_y, roi_start_x:roi_end_x] = 255
    
    # Apply ROI to all color masks
    masked_masks = {}
    for color_name, mask in masks.items():
        if mask is not None:
            masked_masks[color_name] = cv2.bitwise_and(mask, roi_mask)
        else:
            masked_masks[color_name] = mask
    
    # Create a cropped frame for processing (optional - you can keep full frame for display)
    roi_frame = frame.copy()
    
    return roi_frame, masked_masks
from datetime import datetime

class TraySizeLearner:
    """Learn expected tray dimensions to split merged regions."""
    def __init__(self, n=50, tol=0.25):
        self.n = n
        self.tol = tol  # tolerance for size matching
        self.widths = deque(maxlen=n)
        self.heights = deque(maxlen=n)
        self.learned_w = None
        self.learned_h = None
        
    def add_sample(self, w, h):
        """Add a tray dimension sample."""
        self.widths.append(w)
        self.heights.append(h)
        if len(self.widths) >= 10:  # Update estimates
            self.learned_w = np.median(list(self.widths))
            self.learned_h = np.median(list(self.heights))
    
    def is_single_tray(self, w, h):
        """Check if dimensions match a single tray."""
        if self.learned_w is None or self.learned_h is None:
            return True  # No learned size yet
        
        w_ratio = w / self.learned_w if self.learned_w > 0 else 1
        h_ratio = h / self.learned_h if self.learned_h > 0 else 1
        
        # Within tolerance of learned single tray size
        return (1 - self.tol) <= w_ratio <= (1 + self.tol) and (1 - self.tol) <= h_ratio <= (1 + self.tol)
    
    def is_double_tray(self, w, h):
        """Check if dimensions suggest two trays side by side."""
        if self.learned_w is None or self.learned_h is None:
            return False
            
        # Check for approximately 2x width (side by side) or 2x height (stacked)
        w_ratio = w / (2 * self.learned_w) if self.learned_w > 0 else 1
        h_ratio = h / (2 * self.learned_h) if self.learned_h > 0 else 1
        
        side_by_side = (1 - self.tol) <= w_ratio <= (1 + self.tol) and (1 - self.tol) <= h / self.learned_h <= (1 + self.tol)
        stacked = (1 - self.tol) <= h_ratio <= (1 + self.tol) and (1 - self.tol) <= w / self.learned_w <= (1 + self.tol)
        
        return side_by_side or stacked

def find_trays(frame, tray_mask, learner, max_trays=2):
    """Find tray bounding boxes using size learning and valley detection."""
    h, w = frame.shape[:2]
    
    def iou(a,b):
        ax,ay,aw,ah = a; bx,by,bw,bh = b
        xa, ya = max(ax,bx), max(ay,by)
        xb, yb = min(ax+aw, bx+bw), min(ay+ah, by+bh)
        inter = max(0, xb-xa) * max(0, yb-ya)
        union = aw*ah + bw*bh - inter + 1e-6
        return inter/union
    
    # Find all tray contours
    contours, _ = cv2.findContours(tray_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if not contours:
        return []
    
    # Get bounding rectangles for all significant contours
    min_area = (w * h) * 0.035  # accept a bit smaller so split can trigger
    candidates = []
    
    for contour in contours:
        if cv2.contourArea(contour) > min_area:
            x, y, cw, ch = cv2.boundingRect(contour)
            candidates.append((x, y, cw, ch))
    
    if not candidates:
        return []
    
    # Sort by area, largest first
    candidates.sort(key=lambda box: box[2] * box[3], reverse=True)
    
    result_boxes = []
    
    for x, y, cw, ch in candidates:
        # Add to learner if it looks like a reasonable single tray
        if learner.is_single_tray(cw, ch):
            learner.add_sample(cw, ch)
            result_boxes.append((x, y, cw, ch))
        elif learner.is_double_tray(cw, ch):
            # Try to split this region
            split_boxes = split_merged_tray(frame[y:y+ch, x:x+cw], learner, (x, y))
            result_boxes.extend(split_boxes)
        else:
            # Unknown size - accept as is but don't learn from it
            result_boxes.append((x, y, cw, ch))
            
        if len(result_boxes) >= max_trays:
            break

    # Non-maximum suppression to remove near-duplicates
    kept = []
    for box in result_boxes:
        if all(iou(box, k) < 0.5 for k in kept):
            kept.append(box)

    return kept[:max_trays]

def split_merged_tray(crop, learner, offset=(0, 0)):
    """Split a merged tray region using projection and watershed."""
    h, w = crop.shape[:2]
    ox, oy = offset
    
    # Try horizontal split first (side by side trays)
    if learner.learned_w and w > 1.5 * learner.learned_w:
        # emphasize the tray rails and gaps
        gray = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY) if len(crop.shape) == 3 else crop
        edges = cv2.Canny(gray, 40, 130)
        # prefer gaps (low response) for valleys
        v_proj = np.sum(255 - edges, axis=0)
        
        # Find minimum in the middle region
        mid_start = w // 4
        mid_end = 3 * w // 4
        mid_proj = v_proj[mid_start:mid_end]
        
        if len(mid_proj) > 0:
            # smooth and pick the deepest valley
            mid_proj = cv2.blur(mid_proj.reshape(-1,1), (9,1)).ravel()
            valley_idx = int(np.argmin(mid_proj)) + mid_start
            
            # Split into two boxes
            left_box = (ox, oy, valley_idx, h)
            right_box = (ox + valley_idx, oy, w - valley_idx, h)
            return [left_box, right_box]
    
    # Try vertical split (stacked trays)
    if learner.learned_h and h > 1.5 * learner.learned_h:
        gray = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY) if len(crop.shape) == 3 else crop
        edges = cv2.Canny(gray, 40, 130)
        h_proj = np.sum(255 - edges, axis=1)
        
        mid_start = h // 4
        mid_end = 3 * h // 4
        mid_proj = h_proj[mid_start:mid_end]
        
        if len(mid_proj) > 0:
            mid_proj = cv2.blur(mid_proj.reshape(-1,1), (1,9)).ravel()
            valley_idx = int(np.argmin(mid_proj)) + mid_start
            
            top_box = (ox, oy, w, valley_idx)
            bottom_box = (ox, oy + valley_idx, w, h - valley_idx)
            return [top_box, bottom_box]
    
    # If splitting fails, return original box
    return [(ox, oy, w, h)]

# HSV ranges
RANGES = {
    "green":   ([40, 60, 60],  [85, 255, 255]),
    "yellow":  ([18, 80, 80],  [35, 255, 255]),
    # red wraps around HSV 0 and 180 -> two bands
    "red1":    ([0,  90, 80],  [10, 255, 255]),
    "red2":    ([170, 90, 80], [180, 255, 255]),
    "orange":  ([10, 80, 80],  [18, 255, 255]),
    "blue_b":  ([95, 80, 80],  [130, 255, 255]),
    "tray":    ([95, 60, 40],  [130, 255, 255]),  # a bit wider than blue_b
    "white":   ([0,  0, 180],  [180, 55, 255]),
}

def m(hsv, lo, hi, optimized=True):
    mask = cv2.inRange(hsv, np.array(lo,np.uint8), np.array(hi,np.uint8))
    if optimized:
        # Single morphological operation instead of open+close
        kernel = np.ones((3,3), np.uint8)  # Smaller kernel for speed
        return cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    else:
        kernel = np.ones((5,5), np.uint8)
        return cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=1)

def get_masks(bgr, performance_mode=False):
    # Color stability improvements
    if not performance_mode:
        # Apply median blur to reduce speckle noise (high quality)
        bgr_filtered = cv2.medianBlur(bgr, 3)
        hsv = cv2.cvtColor(bgr_filtered, cv2.COLOR_BGR2HSV)
        
        # Normalize V channel for better color stability
        h, s, v = cv2.split(hsv)
        v = cv2.equalizeHist(v)
        hsv = cv2.merge([h, s, v])
    else:
        # Performance mode - skip filtering
        hsv = cv2.cvtColor(bgr, cv2.COLOR_BGR2HSV)
    
    masks = {}
    for k,(lo,hi) in RANGES.items():
        masks[k] = m(hsv, lo, hi, optimized=performance_mode)
    
    # Unify red/orange as sku candidates
    red = cv2.bitwise_or(masks["red1"], masks["red2"])
    masks["red"] = red
    
    # Blue SKU = blue but not tray plastic
    masks["blue_sku"] = cv2.bitwise_and(masks["blue_b"], cv2.bitwise_not(masks["tray"]))
    
    # Union of all SKU colours (never includes white or tray)
    masks["sku_union"] = cv2.bitwise_or(
        cv2.bitwise_or(masks["green"], masks["yellow"]),
        cv2.bitwise_or(masks["red"], masks["blue_sku"])
    )
    
    return masks

def sku_mask_for(dominant, masks):
    """Pick dominant colour mask; fallback to union if low confidence."""
    # include orange whenever the dominant is red/pink
    if dominant in ("red", "pink"):
        red_orange = cv2.bitwise_or(masks["red"], masks.get("orange", np.zeros_like(masks["red"])))
        return red_orange
    table = {
        "green": masks["green"],
        "yellow": masks["yellow"],
        "blue_sku": masks["blue_sku"]
    }
    return table.get(dominant, masks["sku_union"])

def draw_contours(dst, mask, color, thick=2):
    cnts,_ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if cnts: cv2.drawContours(dst, cnts, -1, color, thick)

def draw_pouches_on(vis, pouch_list, offset=(0,0), color=(0,255,0)):
    """Overlay counted pouches (numbers + outline) onto vis, with optional (x,y) offset."""
    ox, oy = offset
    for i, p in enumerate(pouch_list, start=1):
        if isinstance(p, dict) and "contour" in p:
            cnt = p["contour"]
            # offset contour
            cnt = cnt.copy()
            cnt[:,0,0] += ox
            cnt[:,0,1] += oy
            cv2.drawContours(vis, [cnt], -1, color, 2)
            # label near center if available
            c = p.get("center")
            if c:
                cx, cy = c
                cx += ox; cy += oy
                cv2.circle(vis, (cx, cy), 8, (255,255,255), -1)
                cv2.circle(vis, (cx, cy), 8, color, 2)
                cv2.putText(vis, str(i), (cx-6, cy+6), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0,0,0), 2)
                cv2.putText(vis, str(i), (cx-6, cy+6), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255,255,255), 1)

def create_debug_display(frame, masks, percs, tray_pct_smoothed, state):
    """Create a debug display showing all color masks and analysis."""
    h, w = frame.shape[:2]
    
    # Create larger canvas for debug mode
    text_space_top = 80
    text_space_bottom = 50
    
    # Create a wider layout for debug windows
    debug_h = h // 3
    debug_w = w // 4
    
    canvas_h = h + text_space_top + text_space_bottom
    canvas_w = w * 2 + 100  # Extra space between sections
    
    debug_frame = np.zeros((canvas_h, canvas_w, 3), dtype=np.uint8)
    
    # Original frame on the left with offset
    frame_start_y = text_space_top
    debug_frame[frame_start_y:frame_start_y + h, 50:50 + w] = frame
    
    # Color masks on the right
    colors = {
        'green': (0, 255, 0),
        'yellow': (0, 255, 255), 
        'red': (255, 0, 255),
        'blue_sku': (255, 0, 0),
        'tray': (255, 255, 0),
        'white': (255, 255, 255)
    }
    
    masks_start_x = w + 100
    positions = [
        (masks_start_x, frame_start_y), (masks_start_x + debug_w, frame_start_y),
        (masks_start_x, frame_start_y + debug_h), (masks_start_x + debug_w, frame_start_y + debug_h),
        (masks_start_x, frame_start_y + debug_h * 2), (masks_start_x + debug_w, frame_start_y + debug_h * 2)
    ]
    
    for i, (mask_name, color) in enumerate(colors.items()):
        if i < len(positions):
            x, y = positions[i]
            # Resize mask to fit debug window
            mask_resized = cv2.resize(masks[mask_name], (debug_w, debug_h))
            colored_mask = cv2.cvtColor(mask_resized, cv2.COLOR_GRAY2BGR)
            colored_mask = np.where(colored_mask > 0, color, [50, 50, 50])
            debug_frame[y:y+debug_h, x:x+debug_w] = colored_mask
            
            # Add label with background
            cv2.rectangle(debug_frame, (x, y-25), (x + debug_w, y), (0, 0, 0), -1)
            cv2.putText(debug_frame, f"{mask_name}: {percs.get(mask_name, 0):.1f}%", 
                       (x + 5, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    
    # Top information bar
    cv2.rectangle(debug_frame, (10, 10), (canvas_w-10, 40), (50, 50, 50), -1)
    cv2.rectangle(debug_frame, (10, 10), (canvas_w-10, 40), (255, 255, 255), 1)
    
    cv2.rectangle(debug_frame, (10, 45), (canvas_w-10, 75), (50, 50, 50), -1)
    cv2.rectangle(debug_frame, (10, 45), (canvas_w-10, 75), (255, 255, 255), 1)
    
    cv2.putText(debug_frame, f"DEBUG MODE - Tray Detection: {tray_pct_smoothed:.1f}% | State: {state}", 
               (20, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
    
    cv2.putText(debug_frame, f"Original Video (Left) | Color Masks Analysis (Right)", 
               (20, 65), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # Bottom information
    cv2.rectangle(debug_frame, (10, canvas_h-40), (canvas_w-10, canvas_h-10), (50, 50, 50), -1)
    cv2.rectangle(debug_frame, (10, canvas_h-40), (canvas_w-10, canvas_h-10), (255, 255, 255), 1)
    
    cv2.putText(debug_frame, f"Press 'd' to exit debug mode | Press 'q' to quit", 
               (20, canvas_h-20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
    
    return debug_frame

def truthful_pouch_count(bgr, masks, dominant_color, show_markers=False):
    """
    More truthful pouch counting using white-first instance split,
    then validate with dominant SKU color overlap. Includes size sanity and temporal smoothing.
    """
    h, w = bgr.shape[:2]
    vis = bgr.copy()
    
    # Work inside the tray (not over rims) - shrink tray ROI
    tray_mask = masks.get("tray", np.zeros_like(bgr[:,:,0]))
    k = cv2.getStructuringElement(cv2.MORPH_RECT, (17,17))
    inner_tray = cv2.erode(tray_mask, k, iterations=1)
    
    # White mask excluding tray
    white_mask = cv2.bitwise_and(masks["white"], cv2.bitwise_not(tray_mask))
    white_mask = cv2.bitwise_and(white_mask, inner_tray)  # Work only in safe inner region
    
    if np.count_nonzero(white_mask) == 0:
        return 0, vis, []
    
    # Distance transform + peaks for watershed separation
    dist = cv2.distanceTransform(white_mask, cv2.DIST_L2, 3)
    if dist.max() <= 0:
        return 0, vis, []
    
    # Find peaks - easier seeding (helps when pouches touch)
    peaks = (dist > (0.22*dist.max())).astype(np.uint8)*255
    peaks = cv2.morphologyEx(peaks, cv2.MORPH_OPEN, np.ones((5,5),np.uint8), iterations=1)
    
    # Watershed segmentation for touching pouches
    contours, _ = cv2.findContours(peaks, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    markers = np.zeros(white_mask.shape, dtype=np.int32)
    
    for i, contour in enumerate(contours):
        cv2.drawContours(markers, [contour], -1, i+1, -1)
    
    if np.max(markers) > 0:
        # Apply watershed
        white_3ch = cv2.cvtColor(white_mask, cv2.COLOR_GRAY2BGR)
        cv2.watershed(white_3ch, markers)
    
    # Extract candidate pouches and validate with dominant SKU color overlap
    candidates = []
    sku_mask = sku_mask_for(dominant_color, masks) if dominant_color != "none" else masks["sku_union"]
    
    for label in range(1, np.max(markers) + 1):
        instance_mask = (markers == label).astype(np.uint8) * 255
        if np.count_nonzero(instance_mask) == 0:
            continue
            
        # Check SKU overlap - keep only blobs that overlap dominant color ≥ 10%
        overlap = cv2.bitwise_and(instance_mask, sku_mask)
        overlap_ratio = np.count_nonzero(overlap) / max(1, np.count_nonzero(instance_mask))
        
        if overlap_ratio >= 0.04:  # 4% is enough; red+orange areas are small on each pouch
            # Find contour for this instance
            inst_contours, _ = cv2.findContours(instance_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if inst_contours:
                candidates.extend(inst_contours)
    
    if not candidates:
        return 0, vis, []
    
    # Size sanity per pouch - use median area filtering
    areas = [cv2.contourArea(c) for c in candidates]
    if not areas:
        return 0, vis, []
        
    med_area = np.median(areas)
    filtered_contours = [c for c in candidates if 0.4*med_area <= cv2.contourArea(c) <= 2.2*med_area]
    
    count = len(filtered_contours)
    
    # Clamp on expected range + temporal smoothing for milk trays
    expected = 12
    if count > 16 or count < 6:
        if hasattr(truthful_pouch_count, 'last_good') and truthful_pouch_count.last_good is not None:
            count = truthful_pouch_count.last_good
    else:
        truthful_pouch_count.last_good = count
    
    # Temporal smoothing
    if hasattr(truthful_pouch_count, 'last_count'):
        count = int(0.5 * truthful_pouch_count.last_count + 0.5 * count)
    truthful_pouch_count.last_count = count
    
    # Visualization
    if show_markers and filtered_contours:
        colors = [(0, 255, 0), (255, 0, 255), (0, 255, 255), (255, 255, 0), 
                 (255, 128, 0), (128, 0, 255), (255, 0, 128), (128, 255, 0)]
        
        for i, contour in enumerate(filtered_contours):
            color = colors[i % len(colors)]
            cv2.drawContours(vis, [contour], -1, color, 2)
            
            # Center point
            M = cv2.moments(contour)
            if M["m00"] > 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                cv2.circle(vis, (cx, cy), 8, color, -1)
                cv2.putText(vis, str(i+1), (cx-5, cy+5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
    
    # Create pouch data for compatibility
    pouch_data = []
    for i, contour in enumerate(filtered_contours):
        M = cv2.moments(contour)
        if M["m00"] > 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            area = cv2.contourArea(contour)
            x, y, cw, ch = cv2.boundingRect(contour)
            
            pouch_data.append({
                'contour': contour,
                'center': (cx, cy),
                'area': area,
                'bbox': (x, y, cw, ch),
                'detection_strategy': 'truthful_white_first'
            })
    
    return count, vis, pouch_data

def approx_pouch_count(bgr, tray_mask, show_markers=False):
    """Find largest tray region and count white 'pouch centers' via distance peaks."""
    cnts,_ = cv2.findContours(tray_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if not cnts: return 0, bgr, []
    c = max(cnts, key=cv2.contourArea)
    x,y,w,h = cv2.boundingRect(c)
    crop = bgr[y:y+h, x:x+w]
    hsv = cv2.cvtColor(crop, cv2.COLOR_BGR2HSV)
    white = m(hsv, RANGES["white"][0], RANGES["white"][1])
    tray  = m(hsv, RANGES["tray"][0],  RANGES["tray"][1])
    white = cv2.bitwise_and(white, cv2.bitwise_not(tray))
    white = cv2.morphologyEx(white, cv2.MORPH_CLOSE, np.ones((7,7),np.uint8), iterations=1)
    dist = cv2.distanceTransform(white, cv2.DIST_L2, 3)
    if dist.max() <= 0: return 0, bgr, []
    peaks = (dist > (0.35*dist.max())).astype(np.uint8)*255
    peaks = cv2.morphologyEx(peaks, cv2.MORPH_OPEN, np.ones((9,9),np.uint8), iterations=1)
    
    # Find pouch centers
    pouch_centers = []
    cnts_peaks,_ = cv2.findContours(peaks, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for cnt in cnts_peaks:
        M = cv2.moments(cnt)
        if M["m00"] > 0:
            cx = int(M["m10"] / M["m00"]) + x
            cy = int(M["m01"] / M["m00"]) + y
            pouch_centers.append((cx, cy))
    
    count = len(pouch_centers)
    vis = bgr.copy()
    cv2.rectangle(vis, (x,y), (x+w,y+h), (255,0,0), 2)
    
    # Mark pouches if requested
    if show_markers:
        for i, (cx, cy) in enumerate(pouch_centers):
            cv2.circle(vis, (cx, cy), 8, (0, 255, 0), 2)  # Green circles
            cv2.putText(vis, str(i+1), (cx-5, cy+5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    return count, vis, pouch_centers

# Initialize static variables for truthful_pouch_count
truthful_pouch_count.last_count = None
truthful_pouch_count.last_good = None

def ultra_sensitive_green_detection(bgr, masks, show_markers=False, debug_mode=False, use_watershed=False):
    """
    Ultra-sensitive green pouch detection - catches even small green regions.
    Uses multiple detection strategies and very lenient thresholds.
    Optional watershed segmentation for overlapping pouches.
    """
    h, w = bgr.shape[:2]
    vis = bgr.copy()
    pouches = []
    debug_info = []
    
    # Get the green mask and enhance it aggressively
    green_mask = masks.get("green", np.zeros_like(bgr[:,:,0]))
    
    # Apply watershed separation for overlapping pouches if enabled
    if use_watershed and np.count_nonzero(green_mask) > 0:
        labels, separated_masks = watershed_pouch_separation(green_mask)
        print(f"🔬 Watershed: {len(separated_masks)} separated regions")
        # Process each separated region
        all_pouches = []
        for i, sep_mask in enumerate(separated_masks):
            sub_pouches = process_green_mask_region(sep_mask, bgr, vis, show_markers, debug_mode, f"W{i+1}")
            all_pouches.extend(sub_pouches)
        return len(all_pouches), vis, all_pouches, {
            'total_contours': len(all_pouches),
            'valid_pouches': len(all_pouches), 
            'rejected_pouches': 0,
            'area_range': (0, 0),
            'rejection_reasons': {},
            'watershed_regions': len(separated_masks)
        }
    
    # Standard processing without watershed
    return process_green_mask_region(green_mask, bgr, vis, show_markers, debug_mode)

def process_green_mask_region(green_mask, bgr, vis, show_markers=False, debug_mode=False, region_id=""):
    """Process a single green mask region for pouch detection."""
    h, w = bgr.shape[:2]
    pouches = []
    
    # Multiple enhancement strategies
    kernel_small = np.ones((3, 3), np.uint8)
    kernel_medium = np.ones((5, 5), np.uint8)
    
    # Strategy 1: Minimal processing for maximum sensitivity
    enhanced1 = cv2.morphologyEx(green_mask, cv2.MORPH_CLOSE, kernel_small, iterations=1)
    
    # Strategy 2: Medium processing for connected regions
    enhanced2 = cv2.morphologyEx(green_mask, cv2.MORPH_CLOSE, kernel_medium, iterations=1)
    enhanced2 = cv2.morphologyEx(enhanced2, cv2.MORPH_OPEN, kernel_small, iterations=1)
    
    # Strategy 3: Dilate then erode to connect nearby regions
    enhanced3 = cv2.dilate(green_mask, kernel_small, iterations=2)
    enhanced3 = cv2.erode(enhanced3, kernel_small, iterations=1)
    
    # Combine all strategies
    combined_mask = cv2.bitwise_or(enhanced1, enhanced2)
    combined_mask = cv2.bitwise_or(combined_mask, enhanced3)
    
    # Find contours with maximum sensitivity
    contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # ULTRA-LENIENT thresholds
    min_area = (w * h) * 0.0005  # 0.05% - much smaller threshold!
    max_area = (w * h) * 0.25    # 25% - larger maximum
    
    valid_pouches = []
    rejected_pouches = []
    
    for i, contour in enumerate(contours):
        area = cv2.contourArea(contour)
        rejection_reason = None
        
        if area < min_area:
            rejection_reason = f"too_small_area_{int(area)}"
        elif area > max_area:
            rejection_reason = f"too_large_area_{int(area)}"
        else:
            # Get bounding rectangle
            x, y, cw, ch = cv2.boundingRect(contour)
            aspect_ratio = float(cw) / ch if ch > 0 else 1.0
            
            # VERY lenient aspect ratio - almost anything goes
            if 0.1 < aspect_ratio < 10.0:
                # Calculate contour properties
                M = cv2.moments(contour)
                if M["m00"] > 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    # Very lenient solidity check
                    hull = cv2.convexHull(contour)
                    hull_area = cv2.contourArea(hull)
                    solidity = area / hull_area if hull_area > 0 else 0
                    
                    # VERY lenient solidity - accept almost anything
                    if solidity > 0.3:
                        pouch_info = {
                            'contour': contour,
                            'center': (cx, cy),
                            'area': area,
                            'bbox': (x, y, cw, ch),
                            'aspect_ratio': aspect_ratio,
                            'solidity': solidity,
                            'detection_strategy': 'ultra_sensitive'
                        }
                        valid_pouches.append(pouch_info)
                    else:
                        rejection_reason = f"low_solidity_{solidity:.2f}"
                else:
                    rejection_reason = "no_moments"
            else:
                rejection_reason = f"bad_aspect_ratio_{aspect_ratio:.2f}"
        
        # Track rejections for debug
        if rejection_reason:
            rejected_pouches.append({
                'contour': contour,
                'area': area,
                'reason': rejection_reason
            })
    
    # Sort by area (largest first) but don't remove overlaps too aggressively
    valid_pouches.sort(key=lambda p: p['area'], reverse=True)
    
    # Very lenient overlap removal - only remove if centers are VERY close
    final_pouches = []
    for pouch in valid_pouches:
        is_unique = True
        cx, cy = pouch['center']
        
        for existing_pouch in final_pouches:
            ex, ey = existing_pouch['center']
            distance = np.sqrt((cx - ex)**2 + (cy - ey)**2)
            
            # Only reject if centers are extremely close (much more lenient)
            overlap_threshold = min(pouch['bbox'][2], pouch['bbox'][3]) * 0.3
            if distance < overlap_threshold:
                is_unique = False
                break
        
        if is_unique:
            final_pouches.append(pouch)
    
    pouches = final_pouches
    
    # Enhanced visualization with debug info
    if show_markers and pouches:
        colors = [
            (0, 255, 0),    # Bright Green
            (255, 0, 255),  # Magenta  
            (0, 255, 255),  # Cyan
            (255, 255, 0),  # Yellow
            (255, 128, 0),  # Orange
            (128, 0, 255),  # Purple
            (255, 0, 128),  # Pink
            (128, 255, 0),  # Lime
            (0, 128, 255),  # Light Blue
            (255, 128, 128) # Light Red
        ]
        
        for i, pouch in enumerate(pouches):
            contour = pouch['contour']
            center = pouch['center']
            bbox = pouch['bbox']
            color = colors[i % len(colors)]
            
            # Draw filled contour for better visibility
            cv2.drawContours(vis, [contour], -1, color, -1, cv2.LINE_AA)
            cv2.drawContours(vis, [contour], -1, (0, 0, 0), 2, cv2.LINE_AA)  # Black outline
            
            # Large center point
            cv2.circle(vis, center, 12, (255, 255, 255), -1)
            cv2.circle(vis, center, 12, color, 3)
            
            # Bold number
            cv2.putText(vis, str(i + 1), (center[0] - 8, center[1] + 6), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 3, cv2.LINE_AA)
            cv2.putText(vis, str(i + 1), (center[0] - 8, center[1] + 6), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2, cv2.LINE_AA)
            
            # Bounding box with area info
            x, y, cw, ch = bbox
            cv2.rectangle(vis, (x, y), (x + cw, y + ch), color, 2)
            
            # Area and quality info
            area_text = f"A:{int(pouch['area'])}"
            cv2.putText(vis, area_text, (x, y - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1, cv2.LINE_AA)
    
    # Debug mode: show rejected contours in red
    if debug_mode and show_markers:
        for rejected in rejected_pouches[:20]:  # Show first 20 rejections
            cv2.drawContours(vis, [rejected['contour']], -1, (0, 0, 255), 1)
            # Add rejection reason
            M = cv2.moments(rejected['contour'])
            if M["m00"] > 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                cv2.putText(vis, "X", (cx-5, cy+5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 255), 1)
    
    debug_info = {
        'total_contours': len(contours),
        'valid_pouches': len(pouches),
        'rejected_pouches': len(rejected_pouches),
        'area_range': f"{min_area:.0f}-{max_area:.0f}",
        'rejection_reasons': [r['reason'] for r in rejected_pouches[:10]]
    }
    
    return len(pouches), vis, pouches, debug_info

def hybrid_pouch_detection(bgr, masks, dominant_color, show_markers=False):
    """
    Hybrid approach: Use ultra-sensitive detection with fallback options.
    Always tries to get the maximum number of pouches possible.
    """
    # Method 1: Ultra-sensitive green detection (primary method)
    count1, vis1, pouches_advanced, debug_info = ultra_sensitive_green_detection(bgr, masks, show_markers, False)
    
    # Method 2: Traditional distance transform on white areas (fallback)
    tray_mask = masks.get("tray", np.zeros_like(bgr[:,:,0]))
    count2, vis2, pouches_traditional = approx_pouch_count(bgr, tray_mask, False)
    
    # Always prefer the ultra-sensitive method unless it finds nothing
    if count1 > 0:
        return count1, vis1, pouches_advanced
    else:
        # If ultra-sensitive finds nothing, fall back to traditional
        return count2, vis2, pouches_traditional

def advanced_pouch_detection(bgr, masks, dominant_color, show_markers=False):
    """
    Advanced detection - wrapper for ultra-sensitive detection.
    """
    count, vis, pouches, debug_info = ultra_sensitive_green_detection(bgr, masks, show_markers, True)
    return count, vis, pouches

def tray_rois(tray_mask, frame_shape, min_area=0.08):
    """Find multiple tray regions for per-tray counting."""
    cnts,_ = cv2.findContours(tray_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    areas = frame_shape[0] * frame_shape[1]
    rois = [cv2.boundingRect(c) for c in cnts if cv2.contourArea(c) > min_area*areas]
    return sorted(rois, key=lambda r: r[1])  # top→bottom

def overlap_with_line(box, band):
    """Calculate overlap between a tray box and the tripline band."""
    x, y, w, h = box
    y0, y1, xL, xR = band
    xa = max(x, xL)
    xb = min(x + w, xR)
    ya = max(y, y0)
    yb = min(y + h, y1)
    return max(0, xb - xa) * max(0, yb - ya)

def analyze_detection_parameters(bgr, masks, dominant_color):
    """
    Analyze current frame to suggest optimal detection parameters.
    This helps tune the detection for different lighting conditions and pouch arrangements.
    """
    h, w = bgr.shape[:2]
    total_pixels = h * w
    
    analysis = {
        'frame_size': (w, h),
        'total_pixels': total_pixels,
        'dominant_color': dominant_color,
        'color_percentages': {}
    }
    
    # Calculate color percentages
    for color_name, mask in masks.items():
        if color_name != 'white':  # Skip white for SKU analysis
            pixels = np.count_nonzero(mask)
            percentage = (pixels / total_pixels) * 100
            analysis['color_percentages'][color_name] = percentage
    
    # Suggest optimal area thresholds based on frame size
    suggested_min_area = total_pixels * 0.003  # 0.3% for smaller pouches
    suggested_max_area = total_pixels * 0.12   # 12% for larger pouches
    
    analysis['suggested_thresholds'] = {
        'min_area': int(suggested_min_area),
        'max_area': int(suggested_max_area),
        'min_area_percent': 0.3,
        'max_area_percent': 12.0
    }
    
    return analysis

def color_pouch_count(bgr, masks, dominant_color, show_markers=False):
    """Color-only pouch detection fallback when truthful method finds too few."""
    vis = bgr.copy()
    sku = sku_mask_for(dominant_color, masks) if dominant_color != "none" else masks["sku_union"]
    if sku is None: 
        return 0, vis, []
    
    # gentle clean/merge
    k3 = np.ones((3,3), np.uint8)
    k5 = np.ones((5,5), np.uint8)
    mask = cv2.morphologyEx(sku, cv2.MORPH_CLOSE, k5, iterations=1)
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN,  k3, iterations=1)

    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    h, w = bgr.shape[:2]
    min_a = 0.0025 * w * h     # ~0.25%
    max_a = 0.12   * w * h     # 12%

    pouches = []
    for c in contours:
        a = cv2.contourArea(c)
        if a < min_a or a > max_a: 
            continue
        x,y,cw,ch = cv2.boundingRect(c)
        ar = cw/float(ch+1e-6)
        if not (0.15 < ar < 8.0): 
            continue
        M = cv2.moments(c)
        if M["m00"] <= 0: 
            continue
        cx, cy = int(M["m10"]/M["m00"]), int(M["m01"]/M["m00"])
        pouches.append({"contour": c, "center": (cx,cy), "area": a, "bbox": (x,y,cw,ch), "detection_strategy": "color_only"})
        if show_markers:
            cv2.drawContours(vis, [c], -1, (0,255,0), 2)
            cv2.circle(vis, (cx,cy), 8, (255,255,255), -1)
            cv2.circle(vis, (cx,cy), 8, (0,255,0), 2)
            cv2.putText(vis, str(len(pouches)), (cx-6,cy+6), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0,0,0), 2)
            cv2.putText(vis, str(len(pouches)), (cx-6,cy+6), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255,255,255), 1)
    return len(pouches), vis, pouches

def main():
    global roi_selected, drawing, roi_start_x, roi_start_y, roi_end_x, roi_end_y, current_frame
    
    ap = argparse.ArgumentParser()
    ap.add_argument("--source", default="0", help="camera index or media path")
    ap.add_argument("--csv", default=None, help="optional CSV log for counts")
    ap.add_argument("--trip_y", type=float, default=0.85, help="tripline y as fraction of height (default 0.85)")
    ap.add_argument("--enter", type=float, default=12.0, help="tray-blue %% to trigger ENTER")
    ap.add_argument("--exit",  type=float, default=6.0,  help="tray-blue %% to trigger EXIT (hysteresis)")
    ap.add_argument("--max_width", type=int, default=800, help="maximum video width for performance (default 800)")
    ap.add_argument("--save_hsv", default=None, help="save HSV ranges to JSON file")
    ap.add_argument("--load_hsv", default=None, help="load HSV ranges from JSON file")
    ap.add_argument("--event_output", action="store_true", help="emit JSON events for integration")
    ap.add_argument("--line_xmin", type=float, default=0.18, help="tripline left edge as fraction of width (default 0.18)")
    ap.add_argument("--line_xmax", type=float, default=0.82, help="tripline right edge as fraction of width (default 0.82)")
    ap.add_argument("--learn_tray_frames", type=int, default=50, help="frames to learn tray size (default 50)")
    ap.add_argument("--dominant_min_pct", type=float, default=2.0, help="minimum % for dominant SKU (default 2.0)")
    ap.add_argument("--dominant_min_conf", type=float, default=0.5, help="minimum confidence for dominant SKU (default 0.5)")
    args = ap.parse_args()

    # Load HSV configuration if specified
    if args.load_hsv and os.path.exists(args.load_hsv):
        try:
            with open(args.load_hsv, 'r') as f:
                loaded_ranges = json.load(f)
                RANGES.update(loaded_ranges)
                print(f"✅ Loaded HSV ranges from {args.load_hsv}")
        except Exception as e:
            print(f"❌ Failed to load HSV ranges: {e}")

    src = args.source
    if src.isdigit(): src = int(src)
    cap = cv2.VideoCapture(src)
    if not cap.isOpened(): raise SystemExit("Could not open source")

    writer = None
    if args.csv:
        f = open(args.csv, "w", newline="")
        writer = csv.writer(f)
        writer.writerow(["ts","total_crates_passed","dominant_sku","green%","yellow%","red%","blue_sku%","tray_line%","dominant_conf"])

    crate_count = 0
    total_crates_passed = 0  # Track total crates that have completely passed
    state = "IDLE"  # IDLE -> PRESENT
    tray_hist = deque(maxlen=6)  # 5-6 frames smoothing for tray detection
    last_pass_ts = 0
    MIN_GAP_MS = 350
    paused = False
    debug_mode = False
    show_pouch_markers = True
    detection_method = "auto"  # "auto", "truthful", "hybrid", "advanced", "traditional"
    boost_mode = True  # Ultra-sensitive detection by default
    last_frame = None
    bg_model = None  # Background model for motion detection
    
    # Initialize tray learner
    tray_learner = TraySizeLearner(n=args.learn_tray_frames, tol=0.25)

    win = "SKU + Crate Counter"
    cv2.namedWindow(win)
    cv2.setMouseCallback(win, mouse_callback)
    
    print("Controls:")
    print("  q - Quit")
    print("  s - Save current frame")
    print("  p - Pause/Resume video")
    print("  d - Toggle debug mode")
    print("  r - Reset crate counter")
    print("  m - Toggle pouch markers")
    print("  t - Toggle detection method (Auto/Truthful/Hybrid/Advanced/Traditional)")
    print("  z - Select ROI (drag mouse to draw rectangle)")
    print("  c - Clear ROI selection")
    print("  i - Show detection info")
    print("  a - Analyze detection parameters")
    print("  b - Toggle BOOST mode (ultra-sensitive)")
    print("  x - Show debug rejection info")
    frame_skip_counter = 0
    roi_selection_mode = False  # Flag to indicate when ROI selection is active
    
    while True:
        frame_skip_counter += 1
        
        # Performance optimization: skip frames in boost mode
        if boost_mode and frame_skip_counter % 2 != 0 and not paused:
            ret, _ = cap.read()  # Read but don't process
            if not ret: break
            continue
            
        if not paused:
            ret, frame = cap.read()
            if not ret: break
            last_frame = frame.copy()
        else:
            # When paused, use the last frame
            if last_frame is None: 
                paused = False
                continue
            frame = last_frame.copy()
            ret = True
            
        # Resize frame if too large for better performance and display
        original_h, original_w = frame.shape[:2]
        if original_w > args.max_width:
            scale_factor = args.max_width / original_w
            new_w = args.max_width
            new_h = int(original_h * scale_factor)
            frame = cv2.resize(frame, (new_w, new_h))
            
        h,w = frame.shape[:2]
        current_frame = frame.copy()  # Store for ROI selection

        # Tripline ROI once (for visual and overlap math)
        y0 = int(args.trip_y*h)
        band_h = max(12, h//40)
        y1 = max(0, min(h, y0 + band_h))
        y0 = max(0, min(h-1, y0))
        xL = int(args.line_xmin * w)
        xR = int(args.line_xmax * w)

        # Full-frame SKU percentages and improved dominant detection
        performance_mode = boost_mode or not debug_mode  # Use performance mode when boosting or in normal operation
        masks = get_masks(frame, performance_mode)
        
        # Apply ROI masking if ROI is selected
        processed_frame, processed_masks = apply_roi_mask(frame, masks)
        
        total = frame.shape[0]*frame.shape[1]
        
        # Percentages for ALL masks used in debug
        percs_all = {k: 100.0*np.count_nonzero(v) / total for k,v in processed_masks.items()}
        
        # SKU-only percentages for dominant detection
        sku_keys = ["green", "yellow", "red", "blue_sku"]
        percs = {k: percs_all.get(k, 0.0) for k in sku_keys}
        
        # Dominant SKU calculation with confidence
        if any(percs.values()):
            dominant, dom_pct = max(percs.items(), key=lambda kv: kv[1])
            sorted_vals = sorted(percs.values(), reverse=True)
            dominant_conf = sorted_vals[0] - (sorted_vals[1] if len(sorted_vals) > 1 else 0.0)
            
            # Apply stability thresholds
            if dom_pct < args.dominant_min_pct or dominant_conf < args.dominant_min_conf:
                dominant = "none"
        else:
            dominant = "none"
            dom_pct = 0.0
            dominant_conf = 0.0

        # Find tray boxes using improved detection (use processed masks)
        tray_boxes = find_trays(processed_frame, processed_masks["tray"], tray_learner, max_trays=2)
        
        # Calculate tray line coverage using tray boxes instead of motion-gated pixels
        line_band = (y0, y1, xL, xR)
        line_area = (y1 - y0) * (xR - xL)
        cover = sum(overlap_with_line(box, line_band) for box in tray_boxes)
        tray_pct = 100.0 * cover / max(1, line_area)
        
        # Smooth tray detection to prevent flicker
        tray_hist.append(tray_pct)
        tray_pct_smoothed = sum(tray_hist)/len(tray_hist)

        # State machine with smoothing and minimum gap
        now = cv2.getTickCount() / cv2.getTickFrequency() * 1000
        if state == "IDLE" and tray_pct_smoothed >= args.enter and (now - last_pass_ts) > MIN_GAP_MS:
            state = "PRESENT"
            crate_count += 1
        elif state == "PRESENT" and tray_pct_smoothed <= args.exit:
            state = "IDLE"
            total_crates_passed += 1  # A crate has completely passed
            last_pass_ts = now

        # --- PICK COUNTING STRATEGY ---
        use_truthful = False
        if detection_method == "truthful":
            use_truthful = True
        elif detection_method == "auto":
            # Use dominant-SKU driven counter when we have a confident dominant
            use_truthful = (dominant != "none")

        # --- RUN DETECTOR ---
        if use_truthful:
            pcount, vis, pouch_data = truthful_pouch_count(processed_frame, processed_masks, dominant, show_pouch_markers)
            # fallback: if too few found, switch to color-only using the dominant SKU
            if pcount < 6 and dominant != "none":
                pcount, vis, pouch_data = color_pouch_count(processed_frame, processed_masks, dominant, show_pouch_markers)
        elif detection_method == "advanced" or boost_mode:
            pcount, vis, pouch_data = ultra_sensitive_green_detection(processed_frame, processed_masks, show_pouch_markers, debug_mode)[:3]
        elif detection_method == "traditional":
            pcount, vis, pouch_data = approx_pouch_count(processed_frame, processed_masks["tray"], show_pouch_markers)
        else:  # hybrid (default)
            pcount, vis, pouch_data = hybrid_pouch_detection(processed_frame, processed_masks, dominant, show_pouch_markers)

        # Per-tray counting system with improved tray detection
        tray_pouches = []
        tray_details = []
        
        # Draw tray boundaries and count pouches per tray
        for i, (tx, ty, tw, th) in enumerate(tray_boxes, start=1):
            # Draw magenta outline for tray boundary
            cv2.rectangle(vis, (tx, ty), (tx + tw, ty + th), (255, 0, 255), 3)
            cv2.putText(vis, f"T{i}", (tx + 6, ty + 22), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 255), 2)
            
            # Crop frame to tray region
            tray_crop = processed_frame[ty:ty+th, tx:tx+tw]
            if tray_crop.size > 0:
                # Create masks for this tray region
                tray_masks = {k: v[ty:ty+th, tx:tx+tw] for k, v in processed_masks.items() if v is not None}
                
                # run same detector as above so the view & numbers match
                if use_truthful:
                    tray_count, _, tray_pouches_list = truthful_pouch_count(tray_crop, tray_masks, dominant, True)
                    if tray_count < 6 and dominant != "none":
                        tray_count, _, tray_pouches_list = color_pouch_count(tray_crop, tray_masks, dominant, True)
                elif detection_method == "advanced" or boost_mode:
                    tray_count, _, tray_pouches_list = ultra_sensitive_green_detection(tray_crop, tray_masks, True, False)[:3]
                elif detection_method == "traditional":
                    tray_count, _, tray_pouches_list = approx_pouch_count(tray_crop, tray_masks["tray"], True)
                else:
                    tray_count, _, tray_pouches_list = hybrid_pouch_detection(tray_crop, tray_masks, dominant, True)

                # draw those per-tray pouches back onto the main vis with offset
                if isinstance(tray_pouches_list, list) and len(tray_pouches_list) > 0:
                    draw_pouches_on(vis, tray_pouches_list, offset=(tx, ty), color=(0,255,255))
                
                tray_pouches.append(tray_count)
                tray_details.append(f"T{i}:{tray_count}")

        # Display mode selection
        if debug_mode:
            # Show debug display with all color masks (use original masks for debug)
            ann = create_debug_display(frame, masks, percs_all, tray_pct_smoothed, state)
        else:
            # Normal display mode - create expanded canvas
            h, w = frame.shape[:2]
            
            # Draw ROI overlay on the visualization
            vis_with_roi = draw_roi_on_frame(vis)
            
            # Create larger canvas with extra space for text
            text_space_top = 120  # Space at top for text
            text_space_bottom = 80  # Space at bottom for text
            text_space_sides = 200  # Space on sides for text
            
            canvas_h = h + text_space_top + text_space_bottom
            canvas_w = w + text_space_sides * 2
            
            # Create black canvas
            canvas = np.zeros((canvas_h, canvas_w, 3), dtype=np.uint8)
            
            # Place video in center of canvas
            video_start_y = text_space_top
            video_start_x = text_space_sides
            canvas[video_start_y:video_start_y + h, video_start_x:video_start_x + w] = vis_with_roi
            
            # Draw tripline on the video area (accounting for horizontal clipping)
            tripline_y = video_start_y + y0
            tripline_x_start = video_start_x + xL
            tripline_x_end = video_start_x + xR
            cv2.rectangle(canvas, (tripline_x_start, tripline_y), 
                         (tripline_x_end, tripline_y + band_h), (255,0,255), 2)
            
            # Top text area - clear backgrounds
            cv2.rectangle(canvas, (10, 10), (canvas_w-10, 40), (50, 50, 50), -1)
            cv2.rectangle(canvas, (10, 10), (canvas_w-10, 40), (255, 255, 255), 1)
            
            cv2.rectangle(canvas, (10, 45), (canvas_w-10, 75), (50, 50, 50), -1)
            cv2.rectangle(canvas, (10, 45), (canvas_w-10, 75), (255, 255, 255), 1)
            
            cv2.rectangle(canvas, (10, 80), (canvas_w-10, 110), (50, 50, 50), -1)
            cv2.rectangle(canvas, (10, 80), (canvas_w-10, 110), (255, 255, 255), 1)
            
            # Bottom text area
            cv2.rectangle(canvas, (10, canvas_h-70), (canvas_w-10, canvas_h-40), (50, 50, 50), -1)
            cv2.rectangle(canvas, (10, canvas_h-70), (canvas_w-10, canvas_h-40), (255, 255, 255), 1)
            
            cv2.rectangle(canvas, (10, canvas_h-35), (canvas_w-10, canvas_h-5), (50, 50, 50), -1)
            cv2.rectangle(canvas, (10, canvas_h-35), (canvas_w-10, canvas_h-5), (255, 255, 255), 1)
            
            # Status info (pause, debug mode)
            status_text = []
            if paused: status_text.append("PAUSED")
            if debug_mode: status_text.append("DEBUG")
            if show_pouch_markers: status_text.append("MARKERS")
            if boost_mode: status_text.append("BOOST")
            status_text.append(f"METHOD:{detection_method.upper()}")
            if dominant != "none":
                status_text.append(f"COUNT_COLOR:{dominant.upper()}")
            if roi_selected:
                roi_w = roi_end_x - roi_start_x
                roi_h = roi_end_y - roi_start_y
                status_text.append(f"ROI:{roi_w}x{roi_h}")
            
            # Per-tray results display
            if tray_boxes:
                tray_summary = f"TRAYS:{len(tray_boxes)} TOTAL:{sum(tray_pouches)}"
                status_text.append(tray_summary)
                
            if status_text:
                cv2.rectangle(canvas, (canvas_w-220, 10), (canvas_w-10, 40), (0, 100, 0), -1)
                cv2.rectangle(canvas, (canvas_w-220, 10), (canvas_w-10, 40), (255, 255, 255), 1)
                cv2.putText(canvas, " | ".join(status_text), (canvas_w-215, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1, cv2.LINE_AA)
            
            # Main information text - well spaced and clearly visible
            cv2.putText(canvas, f"Tray Detection: {tray_pct_smoothed:.1f}% | State: {state} | Current Crates in Zone: {crate_count}", 
                        (20, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,0,255), 2, cv2.LINE_AA)
            
            cv2.putText(canvas, f"Dominant SKU: {dominant} | Green: {percs['green']:.1f}% | Yellow: {percs['yellow']:.1f}% | Red: {percs['red']:.1f}% | Blue: {percs['blue_sku']:.1f}%",
                        (20, 65), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,0,255), 2, cv2.LINE_AA)
            
            cv2.putText(canvas, f"TOTAL CRATES PASSED: {total_crates_passed} | Pouches in Current Tray: {pcount}", 
                        (20, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,255,0), 2, cv2.LINE_AA)
            
            # Per-tray breakdown if available
            if tray_details:
                tray_text = f"Per-Tray Count: {' | '.join(tray_details)} (Total: {sum(tray_pouches)})"
                if len(tray_text) > 100:  # Truncate if too long
                    tray_text = tray_text[:97] + "..."
                cv2.putText(canvas, tray_text, (20, 130), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0,255,255), 2, cv2.LINE_AA)
            
            # Bottom information
            cv2.putText(canvas, f"Controls: [q]Quit [s]Save [p]Pause [d]Debug [r]Reset [m]Markers [t]Detection [z]ROI [c]Clear ROI", 
                        (20, canvas_h-50), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255,255,0), 2, cv2.LINE_AA)
            
            cv2.putText(canvas, f"Detection: {detection_method.title()} | Video: {w}x{h} | Canvas: {canvas_w}x{canvas_h} | Pouches Found: {len(pouch_data) if hasattr(pouch_data, '__len__') else 'N/A'}", 
                        (20, canvas_h-20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200,200,200), 1, cv2.LINE_AA)
            
            ann = canvas

        # CSV and event logging
        if writer:
            ts = datetime.now().isoformat()
            
            # Calculate dominant confidence (top % - second %)
            sorted_percs = sorted(percs.values(), reverse=True)
            dominant_conf = sorted_percs[0] - (sorted_percs[1] if len(sorted_percs) > 1 else 0)
            
            writer.writerow([ts, total_crates_passed, dominant, percs["green"], percs["yellow"], 
                           percs["red"], percs["blue_sku"], tray_pct_smoothed, dominant_conf])
            
            # Save structured event data for integration
            save_event("frame_analysis", {
                "total_crates_passed": total_crates_passed,
                "dominant_sku": dominant,
                "color_percentages": percs,
                "pouch_count": pcount,
                "tray_count": len(tray_boxes) if tray_boxes else 0,
                "per_tray_counts": tray_pouches if tray_pouches else [],
                "detection_method": detection_method,
                "performance_mode": performance_mode
            })

        cv2.imshow(win, ann)
        key = cv2.waitKey(1) & 0xFF
        
        # Operator-proof key controls with enhanced feedback
        if key == ord('q'): 
            print("🔴 EXITING APPLICATION...")
            save_event("system_shutdown", {"reason": "user_quit"})
            break
        elif key == ord('s'):
            ts = datetime.now().strftime("%Y%m%d_%H%M%S")
            cv2.imwrite(f"frame_{ts}.png", ann)
            save_event("frame_saved", {"filename": f"frame_{ts}.png"})
            print(f"✅ FRAME SAVED: frame_{ts}.png")
        elif key == ord('p'):
            paused = not paused
            status = "🔴 PAUSED" if paused else "▶️ RESUMED"
            save_event("playback_control", {"action": "pause" if paused else "resume"})
            print(f"{status}")
        elif key == ord('d'):
            debug_mode = not debug_mode
            save_event("debug_mode", {"enabled": debug_mode})
            print(f"🔧 DEBUG MODE: {'ON' if debug_mode else 'OFF'}")
        elif key == ord('r'):
            crate_count = 0
            total_crates_passed = 0
            save_event("counter_reset", {"reset_by": "operator"})
            print("🔄 CRATE COUNTERS RESET TO ZERO")
        elif key == ord('m'):
            show_pouch_markers = not show_pouch_markers
            print(f"🔍 POUCH MARKERS: {'ON' if show_pouch_markers else 'OFF'}")
        elif key == ord('t'):
            methods = ["auto", "truthful", "hybrid", "advanced", "traditional"]
            current_idx = methods.index(detection_method)
            detection_method = methods[(current_idx + 1) % len(methods)]
            save_event("detection_method_changed", {"new_method": detection_method})
            print(f"⚙️ DETECTION METHOD: {detection_method.upper()}")
        elif key == ord('b'):
            boost_mode = not boost_mode
            save_event("boost_mode", {"enabled": boost_mode})
            print(f"🚀 BOOST MODE: {'ENABLED - Ultra-sensitive detection' if boost_mode else 'DISABLED - Normal detection'}")
        elif key == ord('x'):
            # Show debug info if available
            if detection_method == "advanced" or boost_mode:
                _, _, _, debug_info = ultra_sensitive_green_detection(frame, masks, False, True)
                print(f"\n=== DEBUG REJECTION INFO ===")
                print(f"Total contours found: {debug_info['total_contours']}")
                print(f"Valid pouches: {debug_info['valid_pouches']}")
                print(f"Rejected pouches: {debug_info['rejected_pouches']}")
                print(f"Area range: {debug_info['area_range']} pixels")
                print(f"Top rejection reasons: {debug_info['rejection_reasons']}")
                print(f"============================\n")
        elif key == ord('z'):
            roi_selection_mode = True
            save_event("roi_selection_mode", {"enabled": True})
            print("🎯 ROI SELECTION MODE: Draw rectangle with mouse to select region")
            print("   - Click and drag to select area")
            print("   - Only pouches in selected area will be counted")
        elif key == ord('c'):
            # Clear ROI selection
            roi_selected = False
            drawing = False
            roi_selection_mode = False
            save_event("roi_cleared", {"cleared_by": "operator"})
            print("🔄 ROI CLEARED - Now processing entire frame")

    cap.release()
    if writer: f.close()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()
