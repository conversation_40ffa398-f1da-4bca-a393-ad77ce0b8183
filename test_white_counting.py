#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for white-based pouch counting implementation
"""

import cv2
import numpy as np
from pathlib import Path
import sys

# Import the updated functions
from v1_optimized import detect_pouches_and_colors_fast, count_pouches_from_white
from config import *

def test_white_counting_on_image(image_path):
    """Test white-based counting on a single image"""
    
    # Load image
    img = cv2.imread(str(image_path))
    if img is None:
        print(f"Could not load image: {image_path}")
        return
    
    print(f"Testing white-based counting on: {image_path}")
    print(f"Image size: {img.shape[1]}x{img.shape[0]}")
    
    # Test the new dual counting function
    try:
        color_counts, color_pouches, white_pouches, dominant_color, color_centers, white_centers = detect_pouches_and_colors_fast(img)
        
        print(f"\n=== RESULTS ===")
        print(f"Color-based count: {color_pouches}")
        print(f"White-based count: {white_pouches}")
        print(f"Dominant color: {dominant_color}")
        print(f"Color breakdown: {dict(color_counts)}")
        print(f"Color centers: {len(color_centers)} points")
        print(f"White centers: {len(white_centers)} points")
        
        # Create visualization
        vis = img.copy()
        
        # Draw color-based detections (colored circles)
        for px, py, color in color_centers:
            color_bgr = POUCH_VISUALIZATION_COLORS.get(color, (128, 128, 128))
            cv2.circle(vis, (px, py), 8, color_bgr, -1)
            cv2.circle(vis, (px, py), 10, (255, 255, 255), 2)
        
        # Draw white-based detections (white circles with black border)
        for px, py in white_centers:
            cv2.circle(vis, (px, py), 6, (255, 255, 255), -1)
            cv2.circle(vis, (px, py), 8, (0, 0, 0), 2)
        
        # Add text overlay
        cv2.putText(vis, f"Color: {color_pouches} | White: {white_pouches}", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)
        cv2.putText(vis, f"Dominant: {dominant_color}", 
                   (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)
        
        # Show results
        cv2.imshow("Original", img)
        cv2.imshow("White + Color Detection", vis)
        
        # Test white-only function separately
        white_centers_only, white_count_only, white_mask = count_pouches_from_white(img)
        print(f"\nWhite-only function: {white_count_only} pouches")
        
        # Show white mask
        cv2.imshow("White Mask", white_mask)
        
        print(f"\nPress any key to continue...")
        cv2.waitKey(0)
        cv2.destroyAllWindows()
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main test function"""
    
    print("=== WHITE-BASED POUCH COUNTING TEST ===")
    print(f"Configuration:")
    print(f"  POUCH_COUNT_MODE: {POUCH_COUNT_MODE}")
    print(f"  LAB_L_MIN: {LAB_L_MIN}")
    print(f"  LAB_AB_TOL: {LAB_AB_TOL}")
    print(f"  DT_PEAK_REL: {DT_PEAK_REL}")
    print(f"  WHITE_OPEN_K: {WHITE_OPEN_K}")
    print(f"  WHITE_CLOSE_K: {WHITE_CLOSE_K}")
    
    # Look for test images in common locations
    test_paths = [
        "test_image.jpg",
        "test_image.png", 
        "sample.jpg",
        "sample.png",
        "tray_images_clean",
        "tray_images_annotated",
        "out/tray_images_clean",
        "out/tray_images_annotated"
    ]
    
    found_images = []
    
    # Check for individual files
    for path_str in test_paths[:4]:
        path = Path(path_str)
        if path.exists():
            found_images.append(path)
    
    # Check directories for images
    for dir_str in test_paths[4:]:
        dir_path = Path(dir_str)
        if dir_path.exists() and dir_path.is_dir():
            for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
                found_images.extend(dir_path.glob(ext))
    
    if not found_images:
        print("\nNo test images found. Please provide an image path as argument.")
        print("Usage: python test_white_counting.py <image_path>")
        print("Or place test images in current directory with names:")
        print("  test_image.jpg, test_image.png, sample.jpg, sample.png")
        return
    
    # Test on found images
    for img_path in found_images[:3]:  # Limit to first 3 images
        test_white_counting_on_image(img_path)
        print("-" * 50)

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Test specific image provided as argument
        test_white_counting_on_image(Path(sys.argv[1]))
    else:
        # Auto-find and test images
        main()
