#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interactive tuning script for white-based pouch detection parameters
"""

import cv2
import numpy as np
import argparse
from pathlib import Path

# Import the functions
from v1_optimized import count_pouches_from_white
from config import *

class WhiteDetectionTuner:
    def __init__(self, image_path):
        self.image = cv2.imread(str(image_path))
        if self.image is None:
            raise ValueError(f"Could not load image: {image_path}")
        
        # Current parameters (start with config defaults)
        self.lab_l_min = LAB_L_MIN
        self.lab_ab_tol = LAB_AB_TOL
        self.dt_peak_rel = DT_PEAK_REL
        self.white_open_k = WHITE_OPEN_K
        self.white_close_k = WHITE_CLOSE_K
        
        # Create windows
        cv2.namedWindow("Original", cv2.WINDOW_NORMAL)
        cv2.namedWindow("White Detection", cv2.WINDOW_NORMAL)
        cv2.namedWindow("Controls", cv2.WINDOW_NORMAL)
        
        # Create trackbars
        cv2.createTrackbar("LAB_L_MIN", "Controls", self.lab_l_min, 255, self.on_lab_l_change)
        cv2.createTrackbar("LAB_AB_TOL", "Controls", self.lab_ab_tol, 50, self.on_lab_ab_change)
        cv2.createTrackbar("DT_PEAK_REL", "Controls", int(self.dt_peak_rel * 100), 100, self.on_dt_peak_change)
        cv2.createTrackbar("WHITE_OPEN_K", "Controls", self.white_open_k, 15, self.on_open_k_change)
        cv2.createTrackbar("WHITE_CLOSE_K", "Controls", self.white_close_k, 15, self.on_close_k_change)
        
        # Initial update
        self.update_detection()
    
    def on_lab_l_change(self, val):
        self.lab_l_min = val
        self.update_detection()
    
    def on_lab_ab_change(self, val):
        self.lab_ab_tol = val
        self.update_detection()
    
    def on_dt_peak_change(self, val):
        self.dt_peak_rel = val / 100.0
        self.update_detection()
    
    def on_open_k_change(self, val):
        self.white_open_k = max(1, val)
        if self.white_open_k % 2 == 0:
            self.white_open_k += 1  # Ensure odd
        self.update_detection()
    
    def on_close_k_change(self, val):
        self.white_close_k = max(1, val)
        if self.white_close_k % 2 == 0:
            self.white_close_k += 1  # Ensure odd
        self.update_detection()
    
    def update_detection(self):
        """Update detection with current parameters"""
        
        # Temporarily modify global config values
        global LAB_L_MIN, LAB_AB_TOL, DT_PEAK_REL, WHITE_OPEN_K, WHITE_CLOSE_K
        old_values = (LAB_L_MIN, LAB_AB_TOL, DT_PEAK_REL, WHITE_OPEN_K, WHITE_CLOSE_K)
        
        LAB_L_MIN = self.lab_l_min
        LAB_AB_TOL = self.lab_ab_tol
        DT_PEAK_REL = self.dt_peak_rel
        WHITE_OPEN_K = self.white_open_k
        WHITE_CLOSE_K = self.white_close_k
        
        try:
            # Run detection
            centers, count, white_mask = count_pouches_from_white(self.image)
            
            # Create visualization
            vis = self.image.copy()
            
            # Draw detected centers
            for px, py in centers:
                cv2.circle(vis, (px, py), 8, (255, 255, 255), -1)
                cv2.circle(vis, (px, py), 10, (0, 0, 0), 2)
            
            # Add count text
            cv2.putText(vis, f"Count: {count}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)
            cv2.putText(vis, f"L_MIN: {self.lab_l_min}", (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(vis, f"AB_TOL: {self.lab_ab_tol}", (10, 100), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(vis, f"PEAK: {self.dt_peak_rel:.2f}", (10, 130), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # Show images
            cv2.imshow("Original", self.image)
            cv2.imshow("White Detection", vis)
            
            # Create control panel image
            control_panel = np.zeros((200, 400, 3), dtype=np.uint8)
            cv2.putText(control_panel, "WHITE DETECTION TUNING", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
            cv2.putText(control_panel, f"Current Count: {count}", (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
            cv2.putText(control_panel, "Adjust trackbars to tune", (10, 100), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
            cv2.putText(control_panel, "Press 's' to save config", (10, 130), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
            cv2.putText(control_panel, "Press 'q' to quit", (10, 160), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
            cv2.imshow("Controls", control_panel)
            
        except Exception as e:
            print(f"Error in detection: {e}")
        
        finally:
            # Restore original values
            LAB_L_MIN, LAB_AB_TOL, DT_PEAK_REL, WHITE_OPEN_K, WHITE_CLOSE_K = old_values
    
    def save_config(self):
        """Save current parameters to a config file"""
        config_text = f"""# Tuned white detection parameters
LAB_L_MIN = {self.lab_l_min}
LAB_AB_TOL = {self.lab_ab_tol}
DT_PEAK_REL = {self.dt_peak_rel}
WHITE_OPEN_K = {self.white_open_k}
WHITE_CLOSE_K = {self.white_close_k}

# To use these parameters, update config.py with the above values
"""
        
        with open("tuned_white_config.txt", "w") as f:
            f.write(config_text)
        
        print(f"Saved tuned parameters to: tuned_white_config.txt")
        print(f"LAB_L_MIN = {self.lab_l_min}")
        print(f"LAB_AB_TOL = {self.lab_ab_tol}")
        print(f"DT_PEAK_REL = {self.dt_peak_rel}")
        print(f"WHITE_OPEN_K = {self.white_open_k}")
        print(f"WHITE_CLOSE_K = {self.white_close_k}")
    
    def run(self):
        """Run the interactive tuning session"""
        print("=== WHITE DETECTION PARAMETER TUNING ===")
        print("Use trackbars to adjust parameters in real-time")
        print("Press 's' to save current parameters")
        print("Press 'q' to quit")
        print("-" * 50)
        
        while True:
            key = cv2.waitKey(30) & 0xFF
            
            if key == ord('q'):
                break
            elif key == ord('s'):
                self.save_config()
        
        cv2.destroyAllWindows()

def main():
    parser = argparse.ArgumentParser(description="Tune white detection parameters")
    parser.add_argument("image", type=str, help="Image file path")
    args = parser.parse_args()
    
    try:
        tuner = WhiteDetectionTuner(args.image)
        tuner.run()
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
