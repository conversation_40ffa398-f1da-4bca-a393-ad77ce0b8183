#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OPTIMIZED Tray detector (OpenCV-only) with significant performance improvements:
- Reduced morphological operations
- Vectorized profile computation
- Cached expensive operations
- Frame skipping for real-time processing
- Optimized memory usage
"""

import os, sys, cv2, time, argparse, numpy as np
from pathlib import Path

# Global counter for tray numbering across all frames
GLOBAL_TRAY_COUNTER = 1
SAVED_TRAY_IDS = set()  # Track which trays have been saved to avoid duplicates

# ------------------------ Global caches for expensive operations ------------------------
_clahe_cache = None
_kernel_cache = {}

def get_clahe():
    """Cached CLAHE object"""
    global _clahe_cache
    if _clahe_cache is None:
        _clahe_cache = cv2.createCLAHE(2.5, (8,8))
    return _clahe_cache

def get_kernel(shape, morph_type):
    """Cached morphological kernels"""
    key = (shape, morph_type)
    if key not in _kernel_cache:
        _kernel_cache[key] = cv2.getStructuringElement(morph_type, shape)
    return _kernel_cache[key]

# ------------------------ Color / Segmentation (Optimized) ------------------------

def blue_mask_fast(bgr):
    """Optimized HSV threshold with reduced morphology operations."""
    hsv = cv2.cvtColor(bgr, cv2.COLOR_BGR2HSV)
    mask = cv2.inRange(hsv, (90,60,40), (140,255,255))
    
    # Reduced morphology - single operation instead of multiple
    kernel = get_kernel((5,5), cv2.MORPH_RECT)
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=1)
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, get_kernel((7,7), cv2.MORPH_RECT), iterations=1)
    
    return mask

# ------------------------ Vectorized 1D profile computation ------------------------

def longest_blue_runs_vectorized(mask, band_frac=0.6, smooth_win=21):
    """Vectorized version of longest blue runs computation."""
    h, w = mask.shape
    band_frac = np.clip(band_frac, 0.2, 0.95)
    xL = int((1.0 - band_frac) * 0.5 * w)
    xR = int((1.0 + band_frac) * 0.5 * w)
    central = (mask[:, xL:xR] > 0).astype(np.uint8)

    # Vectorized computation using numpy operations
    padded = np.pad(central, ((0,0), (1,1)), 'constant')
    diffs = np.diff(padded.astype(np.int16), axis=1)
    
    # Find run lengths efficiently
    runs = np.zeros(h, dtype=np.int32)
    for y in range(h):
        row_diffs = diffs[y]
        starts = np.where(row_diffs == 1)[0]
        ends = np.where(row_diffs == -1)[0]
        if starts.size > 0 and ends.size > 0:
            lens = ends - starts
            runs[y] = int(lens.max()) if lens.size > 0 else 0

    # Optimized convolution
    if smooth_win > 1:
        k = np.ones(smooth_win, np.float32) / float(smooth_win)
        smooth = np.convolve(runs.astype(np.float32), k, mode="same")
    else:
        smooth = runs.astype(np.float32)
    
    return runs, smooth, (xL, xR)

def fuse_profile_with_hough_fast(bgr, smooth, xL, xR, width_frac_thr=0.35, hough_len_frac=0.45, tol=12):
    """Optimized fusion with cached operations."""
    h, w = bgr.shape[:2]
    thr = int((xR - xL) * width_frac_thr)
    cand_rows = np.where(smooth >= thr)[0]

    bands = []
    if cand_rows.size > 0:
        # More efficient grouping
        diff_mask = np.diff(cand_rows) > 10
        if diff_mask.any():
            groups = np.split(cand_rows, np.where(diff_mask)[0] + 1)
        else:
            groups = [cand_rows]
        
        bands = [int(np.mean(g)) for g in groups]

    # Optimized edge detection
    B = bgr[:,:,0]
    clahe = get_clahe()
    enhanced = clahe.apply(B)
    
    # Single-pass Sobel with optimized parameters
    Sob = cv2.Sobel(enhanced, cv2.CV_16S, 0, 1, ksize=3)
    Sob = cv2.convertScaleAbs(Sob)
    
    # Skip Gaussian blur for speed
    _, edge = cv2.threshold(Sob, 34, 255, cv2.THRESH_BINARY)
    edge = cv2.morphologyEx(edge, cv2.MORPH_CLOSE, get_kernel((5,5), cv2.MORPH_RECT), iterations=1)

    # Optimized Hough transform
    min_len = int(w * hough_len_frac)
    lines = cv2.HoughLinesP(edge, 1, np.pi/180, threshold=60, minLineLength=min_len, maxLineGap=25)
    
    hough_ys = []
    if lines is not None:
        # Vectorized horizontal line filtering
        y_diffs = np.abs(lines[:,0,1] - lines[:,0,3])
        horizontal_mask = y_diffs <= 3
        if horizontal_mask.any():
            horizontal_lines = lines[horizontal_mask]
            hough_ys = ((horizontal_lines[:,0,1] + horizontal_lines[:,0,3]) // 2).tolist()

    # Efficient fusion
    fused = []
    if hough_ys:
        hough_ys_array = np.array(hough_ys)
        for y in bands:
            dists = np.abs(hough_ys_array - y)
            min_idx = np.argmin(dists)
            best = hough_ys_array[min_idx]
            fused.append(int(0.5*(best + y)) if dists[min_idx] <= tol else y)
    else:
        fused = bands
    
    return sorted(fused), edge

# ------------------------ Optimized Length + Gap Detection ------------------------

def detect_trays_by_length_gap_fast(mask, xL, xR, len_px_range=(250,430), solid_frac=0.45, gap_blue_max=0.30):
    """Optimized tray detection with vectorized operations."""
    h, w = mask.shape
    central = (mask[:, xL:xR] > 0).astype(np.uint8)
    runs, smooth, _ = longest_blue_runs_vectorized(mask, band_frac=(xR-xL)/w, smooth_win=21)

    band_thr = int((xR - xL) * solid_frac)
    band_rows = np.where(smooth >= band_thr)[0]
    
    bands = []
    if band_rows.size > 0:
        diff_mask = np.diff(band_rows) > 12
        if diff_mask.any():
            groups = np.split(band_rows, np.where(diff_mask)[0] + 1)
        else:
            groups = [band_rows]
        
        bands = [(int(np.mean(g)), int(g[0]), int(g[-1])) for g in groups]

    # Vectorized tray detection
    trays = []
    if len(bands) >= 2:
        for i in range(len(bands)):
            for j in range(i+1, len(bands)):
                y1c, y1s, y1e = bands[i]
                y2c, y2s, y2e = bands[j]
                dist = abs(y2c - y1c)
                
                if len_px_range[0] <= dist <= len_px_range[1]:
                    y_top = min(y1c, y2c)
                    y_bot = max(y1c, y2c)
                    gap_s = min(y1e, y2e)
                    gap_e = max(y1s, y2s)

                    if gap_s >= gap_e:
                        gap_blue_fraction = 0.0
                    else:
                        gap = central[gap_s:gap_e, :]
                        gap_blue_fraction = 0.0 if gap.size == 0 else float((gap.mean(axis=1) >= 0.30).mean())

                    if gap_blue_fraction <= gap_blue_max:
                        trays.append((y_top, y_bot, dist, gap_blue_fraction))

    # Efficient sorting and merging
    if trays:
        target_dist = (len_px_range[0] + len_px_range[1]) / 2
        trays.sort(key=lambda t: (abs(target_dist - t[2]), t[3]))
        
        merged = [trays[0]]
        for t in trays[1:]:
            if abs(t[0] - merged[-1][0]) > 20:
                merged.append(t)
        trays = merged

    return trays, bands, runs, smooth

# ------------------------ Optimized Visualization ------------------------

def draw_overlay_fast(bgr, trays, bands, band, overlay_type="len_gap", prev_lines=None, edge=None, counter=None):
    """Fast overlay drawing with proper bounding boxes for trays."""
    out = bgr.copy()
    h, w = out.shape[:2]
    xL, xR = band
    
    # Draw detection area
    cv2.rectangle(out, (xL,0), (xR,h-1), (255,255,255), 1)
    
    # Define colors for alternating trays
    tray_colors = [
        (0, 255, 0),    # Bright Green
        (255, 0, 0),    # Blue  
        (0, 255, 255),  # Yellow
        (255, 0, 255),  # Magenta
        (0, 165, 255),  # Orange
        (255, 255, 0),  # Cyan
    ]
    
    if overlay_type == "len_gap":
        # Draw bands (detection zones)
        for (yc, ys, ye) in bands:
            cv2.line(out, (0, ys), (w-1, ys), (255,120,0), 1)
            cv2.line(out, (0, ye), (w-1, ye), (255,120,0), 1)
        
        # Draw trays with proper bounding boxes
        for k, (y_top, y_bot, dist, gapQ) in enumerate(trays):
            color = tray_colors[k % len(tray_colors)]
            
            # Draw horizontal lines for tray boundaries
            cv2.line(out, (0, y_top), (w-1, y_top), color, 2)
            cv2.line(out, (0, y_bot), (w-1, y_bot), color, 2)
            
            # Draw bounding rectangle around the entire tray
            tray_margin = 10  # Add some margin around the detection area
            rect_x1 = max(0, xL - tray_margin)
            rect_x2 = min(w-1, xR + tray_margin)
            cv2.rectangle(out, (rect_x1, y_top), (rect_x2, y_bot), color, 3)
            
            # Draw corner markers for better visibility
            corner_size = 20
            # Top-left corner
            cv2.line(out, (rect_x1, y_top), (rect_x1 + corner_size, y_top), color, 5)
            cv2.line(out, (rect_x1, y_top), (rect_x1, y_top + corner_size), color, 5)
            # Top-right corner  
            cv2.line(out, (rect_x2, y_top), (rect_x2 - corner_size, y_top), color, 5)
            cv2.line(out, (rect_x2, y_top), (rect_x2, y_top + corner_size), color, 5)
            # Bottom-left corner
            cv2.line(out, (rect_x1, y_bot), (rect_x1 + corner_size, y_bot), color, 5)
            cv2.line(out, (rect_x1, y_bot), (rect_x1, y_bot - corner_size), color, 5)
            # Bottom-right corner
            cv2.line(out, (rect_x2, y_bot), (rect_x2 - corner_size, y_bot), color, 5)
            cv2.line(out, (rect_x2, y_bot), (rect_x2, y_bot - corner_size), color, 5)
            
            # Add tray label with background - use proper tray numbering
            # If we have a counter and are tracking a tray, show that number
            if counter is not None and counter.current_tray_id is not None:
                # Extract tray number from current_tray_id (format: "tray_N")
                tray_num = int(counter.current_tray_id.split('_')[1]) if counter.current_tray_id else k+1
            else:
                # Fallback to simple increment numbering
                tray_num = k+1
                
            label = f"TRAY #{tray_num}: {dist}px"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            label_x = rect_x1 + 5
            label_y = max(25, y_top - 10)
            
            # Draw label background
            cv2.rectangle(out, (label_x - 2, label_y - label_size[1] - 4), 
                         (label_x + label_size[0] + 4, label_y + 4), (0, 0, 0), -1)
            cv2.rectangle(out, (label_x - 2, label_y - label_size[1] - 4), 
                         (label_x + label_size[0] + 4, label_y + 4), color, 2)
            
            # Draw label text
            cv2.putText(out, label, (label_x, label_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, 
                       color, 2, cv2.LINE_AA)
    
    elif overlay_type == "prev_lines" and prev_lines is not None:
        # Draw previous logic lines with bounding boxes
        for i, y in enumerate(prev_lines):
            color = tray_colors[i % len(tray_colors)]
            cv2.line(out, (0,y), (w-1,y), color, 2)
            
            # Create bounding box for line-based detection
            box_height = 40  # Estimated tray height for line detection
            y_top = max(0, y - box_height//2)
            y_bot = min(h-1, y + box_height//2)
            
            rect_x1 = max(0, xL - 10)
            rect_x2 = min(w-1, xR + 10)
            cv2.rectangle(out, (rect_x1, y_top), (rect_x2, y_bot), color, 3)
            
            cv2.putText(out, f"LINE {i+1}:{y}", (10, y-6), cv2.FONT_HERSHEY_SIMPLEX, 0.5, 
                        (0,0,0), 3, cv2.LINE_AA)
            cv2.putText(out, f"LINE {i+1}:{y}", (10, y-6), cv2.FONT_HERSHEY_SIMPLEX, 0.5, 
                        color, 1, cv2.LINE_AA)
        
        # Show edge detection in corner if available
        if edge is not None:
            e_small = cv2.resize(cv2.cvtColor(edge, cv2.COLOR_GRAY2BGR), (w//4, h//4))
            out[5:5+e_small.shape[0], w - e_small.shape[1]-5: w-5] = e_small
    
    return out

def create_debug_view_fast(overlay, mask, debug_mode=0):
    """Enhanced debug view creation with proper aspect ratio handling."""
    h, w = overlay.shape[:2]
    
    if debug_mode == 0:  # Main LEN+GAP view with small mask
        mask_small = cv2.resize(mask, (w//4, h//4))
        mask_bgr = cv2.cvtColor(mask_small, cv2.COLOR_GRAY2BGR)
        overlay[5:5+mask_bgr.shape[0], w-mask_bgr.shape[1]-5:w-5] = mask_bgr
        return overlay
        
    elif debug_mode == 1:  # LEN+GAP with side mask (properly sized)
        # Resize mask to match overlay height, but limit width to avoid chopping
        mask_width = min(w//2, h)  # Maintain aspect ratio
        mask_resized = cv2.resize(mask, (mask_width, h))
        mask_bgr = cv2.cvtColor(mask_resized, cv2.COLOR_GRAY2BGR)
        
        # Resize overlay to fit remaining space
        overlay_width = w - mask_width
        overlay_resized = cv2.resize(overlay, (overlay_width, h))
        
        return np.hstack([overlay_resized, mask_bgr])
        
    elif debug_mode == 2:  # Previous logic view with small mask
        mask_small = cv2.resize(mask, (w//4, h//4))
        mask_bgr = cv2.cvtColor(mask_small, cv2.COLOR_GRAY2BGR)
        overlay[5:5+mask_bgr.shape[0], w-mask_bgr.shape[1]-5:w-5] = mask_bgr
        return overlay
        
    elif debug_mode == 3:  # Previous logic with side mask (properly sized)
        # Same proper sizing as mode 1
        mask_width = min(w//2, h)
        mask_resized = cv2.resize(mask, (mask_width, h))
        mask_bgr = cv2.cvtColor(mask_resized, cv2.COLOR_GRAY2BGR)
        
        overlay_width = w - mask_width
        overlay_resized = cv2.resize(overlay, (overlay_width, h))
        
        return np.hstack([overlay_resized, mask_bgr])
        
    else:  # Mode 4: Split screen comparison (better layout)
        # Create a 2x1 layout instead of side-by-side chopping
        overlay_small = cv2.resize(overlay, (w, h//2))
        mask_bgr = cv2.cvtColor(cv2.resize(mask, (w, h//2)), cv2.COLOR_GRAY2BGR)
        return np.vstack([overlay_small, mask_bgr])

# ------------------------ Tray ROI Cropping and Saving ------------------------

def save_tray_crops(original_frame, trays, band, outdir, frame_idx=None):
    """
    Crop and save each detected tray as a separate image.
    
    Args:
        original_frame: Original BGR frame
        trays: List of (y_top, y_bot, dist, gapQ) tuples
        band: (xL, xR) detection band coordinates
        outdir: Output directory path
        frame_idx: Optional frame index for naming
    
    Returns:
        List of saved file paths
    """
    global GLOBAL_TRAY_COUNTER
    
    # Create tray_images directory
    tray_dir = Path(outdir) / "tray_images"
    original_dir = Path(outdir) / "original_tray_images"
    tray_dir.mkdir(parents=True, exist_ok=True)
    original_dir.mkdir(parents=True, exist_ok=True)

    saved_files = []
    h, w = original_frame.shape[:2]
    xL, xR = band
    
    for k, (y_top, y_bot, dist, gapQ) in enumerate(trays):
        # Add margin around the tray for better cropping
        crop_margin = 20
        
        # Calculate crop boundaries with margins
        crop_x1 = max(0, xL - crop_margin)
        crop_x2 = min(w, xR + crop_margin)
        crop_y1 = max(0, y_top - crop_margin)
        crop_y2 = min(h, y_bot + crop_margin)
        
        # Crop the tray region
        tray_crop = original_frame[crop_y1:crop_y2, crop_x1:crop_x2]
        
        # Skip if crop is too small
        if tray_crop.shape[0] < 20 or tray_crop.shape[1] < 20:
            continue
        
        # Generate filename with timestamp and global counter
        timestamp = int(time.time() * 1000)
        if frame_idx is not None:
            filename = f"tray_{GLOBAL_TRAY_COUNTER:04d}_frame_{frame_idx:04d}_{timestamp}.png"
        else:
            filename = f"tray_{GLOBAL_TRAY_COUNTER:04d}_{timestamp}.png"
        

        file_path = original_dir / 'original_' + filename

        success = cv2.imwrite(str(file_path), tray_crop)
        print(f"Saved original tray crop: {'original_' + filename} (Size: {tray_crop.shape[1]}x{tray_crop.shape[0]})")


        file_path = tray_dir / filename
        
        # Add info overlay to the crop
        info_crop = tray_crop.copy()
        info_text = f"Tray #{GLOBAL_TRAY_COUNTER} | {dist}px | Gap:{gapQ:.2f}"
        
        # Add semi-transparent background for text
        overlay = info_crop.copy()
        cv2.rectangle(overlay, (5, 5), (info_crop.shape[1]-5, 35), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, info_crop, 0.3, 0, info_crop)
        
        # Add text
        cv2.putText(info_crop, info_text, (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.5, 
                   (0, 255, 255), 1, cv2.LINE_AA)
        
        # Save the cropped tray
        success = cv2.imwrite(str(file_path), info_crop)
        
        if success:
            saved_files.append(str(file_path))
            print(f"Saved tray crop: {filename} (Size: {tray_crop.shape[1]}x{tray_crop.shape[0]})")
        else:
            print(f"Failed to save: {filename}")
        
        GLOBAL_TRAY_COUNTER += 1
    
    return saved_files

def save_tray_crops_once_per_detection(original_frame, trays, band, outdir, counter, frame_idx=None):
    """
    Save tray crops only once per tray detection (when tray first enters gate).
    
    Args:
        original_frame: Original BGR frame
        trays: List of (y_top, y_bot, dist, gapQ) tuples
        band: (xL, xR) detection band coordinates
        outdir: Output directory path
        counter: GateCounter instance to track tray state
        frame_idx: Optional frame index for naming
    
    Returns:
        List of saved file paths
    """
    global GLOBAL_TRAY_COUNTER, SAVED_TRAY_IDS
    
    # Only save when we have a current tray and it hasn't been saved yet
    if counter.current_tray_id is None or counter.current_tray_id in SAVED_TRAY_IDS:
        return []
    
    # Only save when we're in the "INSIDE" state (tray is in gate)
    if counter.state != "INSIDE" or len(trays) == 0:
        return []
    
    # Create separate directories for clean and annotated tray images
    clean_dir = Path(outdir) / "tray_images_clean"
    annotated_dir = Path(outdir) / "tray_images_annotated"
    clean_dir.mkdir(parents=True, exist_ok=True)
    annotated_dir.mkdir(parents=True, exist_ok=True)
    
    saved_files = []
    h, w = original_frame.shape[:2]
    xL, xR = band
    
    # Save only the first detected tray (usually the best detection)
    y_top, y_bot, dist, gapQ = trays[0]
    
    # Add margin around the tray
    crop_margin = 20
    
    # Calculate crop boundaries
    crop_x1 = max(0, xL - crop_margin)
    crop_x2 = min(w, xR + crop_margin)
    crop_y1 = max(0, y_top - crop_margin)
    crop_y2 = min(h, y_bot + crop_margin)
    
    # Crop the tray region
    tray_crop = original_frame[crop_y1:crop_y2, crop_x1:crop_x2]
    
    # Skip if crop is too small
    if tray_crop.shape[0] < 20 or tray_crop.shape[1] < 20:
        return []
    
    # Generate timestamp for filenames
    timestamp = int(time.time() * 1000)
    
    # Save 1) Clean version without any text
    clean_filename = f"tray_{GLOBAL_TRAY_COUNTER:04d}_clean_{timestamp}.png"
    clean_file_path = clean_dir / clean_filename
    success_clean = cv2.imwrite(str(clean_file_path), tray_crop)
    
    # Save 2) Annotated version with text overlay
    info_crop = tray_crop.copy()
    info_text = f"Tray #{GLOBAL_TRAY_COUNTER} | {dist}px | Gap:{gapQ:.2f}"
    
    # Add semi-transparent background for text
    overlay = info_crop.copy()
    cv2.rectangle(overlay, (5, 5), (info_crop.shape[1]-5, 35), (0, 0, 0), -1)
    cv2.addWeighted(overlay, 0.7, info_crop, 0.3, 0, info_crop)
    
    # Add text
    cv2.putText(info_crop, info_text, (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.5, 
               (0, 255, 255), 1, cv2.LINE_AA)
    
    annotated_filename = f"tray_{GLOBAL_TRAY_COUNTER:04d}_annotated_{timestamp}.png"
    annotated_file_path = annotated_dir / annotated_filename
    success_annotated = cv2.imwrite(str(annotated_file_path), info_crop)
    
    # Add both files to saved list if successful
    if success_clean:
        saved_files.append(str(clean_file_path))
    if success_annotated:
        saved_files.append(str(annotated_file_path))
        
    if success_clean or success_annotated:
        print(f"🎯 TRAY DETECTED! Saved to separate folders:")
        if success_clean:
            print(f"   📁 Clean: tray_images_clean/{clean_filename}")
        if success_annotated:
            print(f"   📁 Annotated: tray_images_annotated/{annotated_filename}")
        print(f"   📏 Size: {tray_crop.shape[1]}x{tray_crop.shape[0]}")
        
        # Mark this tray as saved
        SAVED_TRAY_IDS.add(counter.current_tray_id)
        GLOBAL_TRAY_COUNTER += 1
    else:
        print(f"❌ Failed to save tray crop versions")
    
    return saved_files

class FrameController:
    def __init__(self, target_fps=15, process_every_n=2):
        self.target_fps = target_fps
        self.process_every_n = process_every_n
        self.frame_count = 0
        self.last_process_time = time.time()
        self.min_frame_time = 1.0 / target_fps
        self.paused = False
        self.display_fps = target_fps  # For display control
        self.last_display_time = time.time()
        
    def set_fps(self, fps):
        """Manually set target FPS"""
        self.target_fps = max(1, min(fps, 60))  # Limit between 1-60 FPS
        self.display_fps = self.target_fps
        self.min_frame_time = 1.0 / self.target_fps
        print(f"FPS set to: {self.target_fps}")
        
    def increase_fps(self):
        """Increase FPS by 2"""
        self.set_fps(self.target_fps + 2)
        
    def decrease_fps(self):
        """Decrease FPS by 2"""
        self.set_fps(self.target_fps - 2)
        
    def toggle_pause(self):
        """Toggle pause state"""
        self.paused = not self.paused
        print(f"{'Paused' if self.paused else 'Resumed'}")
        if not self.paused:
            self.last_display_time = time.time()
        
    def should_process_frame(self):
        if self.paused:
            return False
            
        self.frame_count += 1
        current_time = time.time()
        
        # Skip frames based on counter
        if self.frame_count % self.process_every_n != 0:
            return False
            
        # Skip frames based on timing
        if current_time - self.last_process_time < self.min_frame_time:
            return False
            
        self.last_process_time = current_time
        return True
        
    def should_display_frame(self):
        """Control display rate independently"""
        if self.paused:
            return True  # Always allow display when paused
            
        current_time = time.time()
        display_interval = 1.0 / self.display_fps
        
        if current_time - self.last_display_time >= display_interval:
            self.last_display_time = current_time
            return True
        return False

# ------------------------ Gate (video): tray start/end ------------------------

class GateCounter:
    def __init__(self, gate_h=80, enter_frames=3, exit_frames=5):
        self.gate_h = gate_h
        self.enter_frames = enter_frames
        self.exit_frames = exit_frames
        self.seen = 0
        self.miss = 0
        self.state = "IDLE"
        self.total = 0
        self.current_tray_id = None  # Track current tray for one-time cropping

    def update(self, H, trays):
        """trays: list of (y_top, y_bot, ...) from LEN+GAP. Return events list."""
        events = []
        in_gate = any((y_bot >= H - self.gate_h) for (y_top, y_bot, *_ ) in trays)
        
        if in_gate:
            self.seen += 1
            self.miss = 0
        else:
            self.seen = 0
            self.miss += 1

        if self.state == "IDLE" and self.seen >= self.enter_frames:
            self.state = "INSIDE"
            self.current_tray_id = f"tray_{self.total + 1}"  # Assign unique ID
            events.append("TRAY_START")
        elif self.state == "INSIDE" and self.miss >= self.exit_frames:
            self.state = "IDLE"
            self.total += 1
            self.current_tray_id = None  # Clear current tray
            events.append("TRAY_END")
        
        return events

# ------------------------ Optimized Runner ------------------------

def process_frame_fast(bgr, args, counter=None, frame_idx=None, save_crops=True):
    """Optimized frame processing with reduced operations and one-time tray cropping."""
    mask = blue_mask_fast(bgr)
    
    # Calculate band coordinates
    xL = int((1.0 - args.band_frac) * 0.5 * bgr.shape[1])
    xR = int((1.0 + args.band_frac) * 0.5 * bgr.shape[1])
    
    # Use optimized detection
    trays, bands, runs, smooth = detect_trays_by_length_gap_fast(
        mask, xL, xR,
        len_px_range=(args.tray_min, args.tray_max),
        solid_frac=args.solid_frac,
        gap_blue_max=args.gap_blue_max
    )
    
    # Save tray crops only once per detection (when tray enters gate)
    if save_crops and len(trays) > 0 and args.save_tray_crops and counter is not None:
        save_tray_crops_once_per_detection(bgr, trays, (xL, xR), args.outdir, counter, frame_idx)
    
    # Get previous logic lines for debug modes
    prev_lines, edge = None, None
    if args.debug_mode >= 2:  # Only compute for prev logic modes
        runs2, smooth2, _ = longest_blue_runs_vectorized(mask, band_frac=args.band_frac, smooth_win=21)
        prev_lines, edge = fuse_profile_with_hough_fast(
            bgr, smooth2, xL, xR,
            width_frac_thr=0.35,  # Using default values
            hough_len_frac=0.45,
            tol=12
        )
    
    # Create appropriate overlay based on debug mode
    if args.debug_mode >= 2:
        overlay = draw_overlay_fast(bgr, trays, bands, (xL, xR), "prev_lines", prev_lines, edge, counter)
    else:
        overlay = draw_overlay_fast(bgr, trays, bands, (xL, xR), "len_gap", None, None, counter)
    
    events = []
    if counter is not None:
        events = counter.update(bgr.shape[0], trays)

    # Enhanced debug view with multiple modes
    view = create_debug_view_fast(overlay, mask, args.debug_mode)
    
    # Enhanced HUD with debug mode info and crop status
    debug_names = ["LEN+GAP", "LEN+GAP+Mask", "Prev+Lines", "Prev+Mask", "Split"]
    mode_name = debug_names[min(args.debug_mode, len(debug_names)-1)]
    crop_status = "CROP:ON" if args.save_tray_crops else "CROP:OFF"
    total_saved = f"Saved:{GLOBAL_TRAY_COUNTER-1}" if args.save_tray_crops else ""
    hud = f"Mode:{args.debug_mode}({mode_name}) Trays:{len(trays)} {crop_status} {total_saved}"
    cv2.putText(view, hud, (10, 24), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0,0,0), 2, cv2.LINE_AA)
    cv2.putText(view, hud, (10, 24), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255,255,255), 1, cv2.LINE_AA)

    if counter is not None:
        H = bgr.shape[0]
        gate_width = view.shape[1] if args.debug_mode in [1, 3, 4] else view.shape[1]//2
        cv2.rectangle(view, (0, H-counter.gate_h), (gate_width, H-1), (255,255,255), 1)
        cv2.putText(view, f"Gate:{counter.gate_h}px State:{counter.state} Total:{counter.total}",
                    (10, 48), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0,0,0), 2, cv2.LINE_AA)
        cv2.putText(view, f"Gate:{counter.gate_h}px State:{counter.state} Total:{counter.total}",
                    (10, 48), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0,255,255), 1, cv2.LINE_AA)

    return view, mask, trays, events

def run_optimized(args):
    """Optimized main runner with frame skipping."""
    input_path = args.input
    is_image = False
    cap = None
    frame = None

    if input_path:
        img = cv2.imread(input_path)
        if img is not None:
            is_image = True
            frame = img
        else:
            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                print("ERROR: cannot open video:", input_path)
                sys.exit(1)
    else:
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("ERROR: cannot open webcam 0")
            sys.exit(1)

    outdir = Path(args.outdir)
    outdir.mkdir(parents=True, exist_ok=True)

    # Create resizable window
    window_name = "Tray Detector - Optimized"
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(window_name, 1200, 800)  # Set initial size

    if is_image:
        # Image processing (no optimization needed for single frame)
        counter = None
        view, mask, trays, events = process_frame_fast(frame, args, counter, save_crops=True)
        out_path = outdir / "annotated_optimized.png"
        mask_path = outdir / "mask_optimized.png"
        cv2.imwrite(str(out_path), view)
        cv2.imwrite(str(mask_path), mask)
        print("Saved:", out_path, mask_path)
        if args.save_tray_crops and len(trays) > 0:
            print(f"Saved {len(trays)} tray crops to: {outdir / 'tray_images'}")
        
        while True:
            cv2.imshow(window_name, view)
            key = cv2.waitKey(0) & 0xFF
            if key == ord('q') or key == 27:
                break
            elif key == ord('d'):
                args.debug_mode = (args.debug_mode + 1) % 5  # 0-4 modes
                view, mask, trays, events = process_frame_fast(frame, args, counter, save_crops=False)  # Don't save again
                print(f"Debug mode: {args.debug_mode}")
            elif key == ord('s'):
                ts = int(time.time()*1000)
                cv2.imwrite(str(outdir / f"snapshot_{ts}.png"), view)
                print(f"Snapshot saved: snapshot_{ts}.png")
            elif key == ord('c'):  # New: Manual crop save
                if len(trays) > 0:
                    saved = save_tray_crops(frame, trays, 
                                          (int((1.0 - args.band_frac) * 0.5 * frame.shape[1]),
                                           int((1.0 + args.band_frac) * 0.5 * frame.shape[1])), 
                                          args.outdir)
                    print(f"Manual crop: Saved {len(saved)} tray images")
                else:
                    print("No trays detected for cropping")
        cv2.destroyAllWindows()
        return

    # Video processing with frame control
    frame_controller = FrameController(target_fps=args.target_fps, process_every_n=args.skip_frames)
    counter = GateCounter(args.gate_h, args.enter_frames, args.exit_frames)
    
    print("OPTIMIZED VERSION WITH MANUAL CONTROLS + TRAY CROPPING")
    print("=" * 60)
    print("Controls:")
    print("  'q' / ESC  : Quit")
    print("  'd'        : Toggle debug modes (0-4)")
    print("  'SPACE'    : Pause/Resume")
    print("  'i'        : Increase FPS (+2)")
    print("  'o'        : Decrease FPS (-2)")
    print("  's'        : Save current frame")
    print("  'c'        : Manual crop current trays")
    print("  't'        : Toggle auto tray cropping ON/OFF")
    print("  'r'        : Reset window size")
    print("=" * 60)
    print("Window is RESIZABLE - drag corners to resize!")
    print(f"Automatic tray cropping: {'ENABLED' if args.save_tray_crops else 'DISABLED'}")
    print(f"Processing every {args.skip_frames} frames, target {args.target_fps} FPS")
    print(f"Tray crops will be saved to:")
    print(f"  📁 Clean: {outdir / 'tray_images_clean'}")
    print(f"  📁 Annotated: {outdir / 'tray_images_annotated'}")
    print("=" * 60)
    
    idx = 0
    last_view = None
    fps_counter = 0
    fps_start_time = time.time()
    
    while True:
        ok, frame = cap.read()
        if not ok:
            break
            
        idx += 1
        
        # Always try to process frames, but control display separately
        should_process = frame_controller.should_process_frame()
        should_display = frame_controller.should_display_frame()
        
        # Process frame only when needed and not paused
        if should_process:
            view, mask, trays, events = process_frame_fast(frame, args, counter, frame_idx=idx, save_crops=True)
            last_view = view
            
            for ev in events:
                print(f"[{idx}] {ev}  total={counter.total}")
        
        # Control display rate with proper FPS limiting
        if should_display and last_view is not None:
            # Add FPS and pause info to the display
            display_view = last_view.copy()
            fps_info = f"FPS: {frame_controller.target_fps} | {'PAUSED' if frame_controller.paused else 'PLAYING'}"
            cv2.putText(display_view, fps_info, (10, display_view.shape[0] - 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,0,0), 3, cv2.LINE_AA)
            cv2.putText(display_view, fps_info, (10, display_view.shape[0] - 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,255,255), 2, cv2.LINE_AA)
            
            controls_info = "SPACE=Pause i/o=FPS d=Debug s=Save c=Crop t=ToggleCrop q=Quit"
            cv2.putText(display_view, controls_info, (10, display_view.shape[0] - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.35, (255,255,255), 1, cv2.LINE_AA)
            
            cv2.imshow(window_name, display_view)

        # Handle key presses with proper timing
        if frame_controller.paused:
            # When paused, wait longer for key response and don't timeout quickly
            key = cv2.waitKey(100) & 0xFF
        elif should_display:
            # Normal FPS control when playing
            wait_time = max(1, int(1000 / frame_controller.target_fps))
            key = cv2.waitKey(wait_time) & 0xFF
        else:
            # Quick check when not displaying
            key = cv2.waitKey(1) & 0xFF
        # Handle key presses and FPS counter
        if not frame_controller.paused and should_process:
            fps_counter += 1
            if fps_counter % 30 == 0:  # Reduced frequency for cleaner output
                elapsed = time.time() - fps_start_time
                if elapsed > 0:
                    current_fps = fps_counter / elapsed
                    print(f"Display FPS: {frame_controller.target_fps} | Processing: {current_fps:.1f}")
        
        # Robust key handling
        if key != 255:  # Only process if a key was actually pressed
            if key == ord('q') or key == 27:  # 'q' or ESC
                print("Exiting...")
                break
            elif key == ord('d'):  # Toggle debug modes
                old_mode = args.debug_mode
                args.debug_mode = (args.debug_mode + 1) % 5  # 0-4 modes
                print(f"Debug mode: {old_mode} -> {args.debug_mode}")
                # Force reprocess current frame with new debug mode
                if last_view is not None and frame is not None:
                    view, mask, trays, events = process_frame_fast(frame, args, counter, save_crops=False)
                    last_view = view
            elif key == ord(' '):  # SPACE - pause/resume
                frame_controller.toggle_pause()
            elif key == ord('i'):  # Increase FPS
                frame_controller.increase_fps()
            elif key == ord('o'):  # Decrease FPS
                frame_controller.decrease_fps()
            elif key == ord('s') and last_view is not None:  # Save frame
                ts = int(time.time()*1000)
                cv2.imwrite(str(outdir / f"frame_optimized_{ts}.png"), last_view)
                print(f"Snapshot saved: frame_optimized_{ts}.png")
            elif key == ord('c') and frame is not None:  # Manual crop current frame
                # Get current trays from last detection
                temp_view, temp_mask, current_trays, temp_events = process_frame_fast(frame, args, counter, save_crops=False)
                if len(current_trays) > 0:
                    xL = int((1.0 - args.band_frac) * 0.5 * frame.shape[1])
                    xR = int((1.0 + args.band_frac) * 0.5 * frame.shape[1])
                    saved = save_tray_crops(frame, current_trays, (xL, xR), args.outdir)
                    print(f"Manual crop: Saved {len(saved)} tray images")
                else:
                    print("No trays detected for manual cropping")
            elif key == ord('t'):  # Toggle automatic tray cropping
                args.save_tray_crops = not args.save_tray_crops
                status = "ENABLED" if args.save_tray_crops else "DISABLED"
                print(f"Automatic tray cropping: {status}")
            elif key == ord('r'):  # New: Reset to original size
                cv2.resizeWindow(window_name, 1200, 800)
                print("Window size reset to 1200x800")

    cap.release()
    cv2.destroyAllWindows()

# ------------------------ CLI ------------------------

def build_argparser():
    ap = argparse.ArgumentParser(description="OPTIMIZED Blue-tray detector with performance improvements")
    ap.add_argument("--input", type=str, default="./videos/2.mp4", help="image path, video path, or leave empty for webcam (default: ./videos/2.mp4)")
    ap.add_argument("--outdir", type=str, default="out", help="directory to save outputs")
    
    # Detection params (same as original)
    ap.add_argument("--band_frac", type=float, default=0.6, help="fraction of width used as central band")
    ap.add_argument("--tray_min", type=int, default=250, help="expected min tray pixel height")
    ap.add_argument("--tray_max", type=int, default=430, help="expected max tray pixel height")
    ap.add_argument("--solid_frac", type=float, default=0.45, help="solid-band width fraction")
    ap.add_argument("--gap_blue_max", type=float, default=0.30, help="max blue fraction in gap")
    
    # Video gate (same as original)
    ap.add_argument("--gate_h", type=int, default=80, help="bottom gate height in px")
    ap.add_argument("--enter_frames", type=int, default=3, help="frames to confirm TRAY_START")
    ap.add_argument("--exit_frames", type=int, default=5, help="frames to confirm TRAY_END")
    
    # Performance optimization parameters
    ap.add_argument("--target_fps", type=int, default=45, help="target processing FPS (default: 25)")
    ap.add_argument("--skip_frames", type=int, default=2, help="process every Nth frame")
    
    # Tray cropping parameters
    ap.add_argument("--save_tray_crops", action="store_true", help="automatically save cropped tray images (one per detection)")
    ap.add_argument("--detailed_crops", action="store_true", help="save crops with info overlay (slower)")
    
    # Simplified debug mode
    ap.add_argument("--debug_mode", type=int, default=0, help="0:LEN+GAP, 1:LEN+GAP+Mask, 2:Prev, 3:Prev+Mask, 4:Split")
    
    return ap

if __name__ == "__main__":
    args = build_argparser().parse_args()
    run_optimized(args)
