#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OPTIMIZED Tray detector (OpenCV-only) with significant performance improvements:
- Reduced morphological operations
- Vectorized profile computation
- Cached expensive operations
- Frame skipping for real-time processing
- Optimized memory usage
"""

import os, sys, cv2, time, argparse, numpy as np
from pathlib import Path
from collections import Counter

# Import all configuration parameters
from config import *

# Global counter for tray numbering across all frames
GLOBAL_TRAY_COUNTER = 1
SAVED_TRAY_IDS = set()  # Track which trays have been saved to avoid duplicates

# Note: All configuration parameters are now imported from config.py
# This includes COLOR_RANGES, TRAY_BLUE_RANGE, WHITE_S_MAX, WHITE_V_MIN, etc.

# ------------------------ Live Tuning System ------------------------

def _nothing(v):
    """Dummy callback for trackbars"""
    pass

def create_tuner(args):
    """Create live tuning trackbar panel for real-time parameter adjustment"""
    cv2.namedWindow("Live Tuner", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("Live Tuner", 400, 600)

    # --- Tray counting sliders ---
    cv2.createTrackbar("tray_min", "Live Tuner", int(args.tray_min), 500, _nothing)
    cv2.createTrackbar("tray_max", "Live Tuner", int(args.tray_max), 600, _nothing)
    cv2.createTrackbar("solid_frac x100", "Live Tuner", int(args.solid_frac*100), 100, _nothing)
    cv2.createTrackbar("gap_blue_max x100", "Live Tuner", int(args.gap_blue_max*100), 100, _nothing)
    cv2.createTrackbar("band_frac x100", "Live Tuner", int(args.band_frac*100), 95, _nothing)

    # --- Pouch counting (white) ---
    cv2.createTrackbar("LAB_L_MIN", "Live Tuner", int(LAB_L_MIN), 255, _nothing)
    cv2.createTrackbar("LAB_AB_TOL", "Live Tuner", int(LAB_AB_TOL), 64, _nothing)
    cv2.createTrackbar("DT_PEAK_REL x100", "Live Tuner", int(DT_PEAK_REL*100), 100, _nothing)
    cv2.createTrackbar("WHITE_OPEN_K", "Live Tuner", int(WHITE_OPEN_K), 9, _nothing)
    cv2.createTrackbar("WHITE_CLOSE_K", "Live Tuner", int(WHITE_CLOSE_K), 9, _nothing)
    cv2.createTrackbar("TRAY_RIM_MARGIN", "Live Tuner", int(TRAY_RIM_MARGIN), 40, _nothing)

    # --- Dominant color / white gate ---
    cv2.createTrackbar("WHITE_S_MAX", "Live Tuner", int(WHITE_S_MAX), 255, _nothing)
    cv2.createTrackbar("WHITE_V_MIN", "Live Tuner", int(WHITE_V_MIN), 255, _nothing)
    cv2.createTrackbar("GATE_EXPAND x1000", "Live Tuner", int(WHITE_GATE_EXPANSION_FACTOR*1000), 50, _nothing)

    print("🎛️  LIVE TUNER CREATED")
    print("   Use trackbars to adjust parameters in real-time")
    print("   Press 'p' to print current values")
    print("   Press 'z' to reset to defaults")

def apply_tuner(args):
    """Read trackbar values and update global parameters"""
    global LAB_L_MIN, LAB_AB_TOL, DT_PEAK_REL
    global WHITE_OPEN_K, WHITE_CLOSE_K, TRAY_RIM_MARGIN
    global WHITE_S_MAX, WHITE_V_MIN, WHITE_GATE_EXPANSION_FACTOR

    # Tray detection parameters
    args.tray_min = cv2.getTrackbarPos("tray_min", "Live Tuner")
    args.tray_max = cv2.getTrackbarPos("tray_max", "Live Tuner")
    args.solid_frac = cv2.getTrackbarPos("solid_frac x100", "Live Tuner") / 100.0
    args.gap_blue_max = cv2.getTrackbarPos("gap_blue_max x100", "Live Tuner") / 100.0
    args.band_frac = max(0.2, cv2.getTrackbarPos("band_frac x100", "Live Tuner") / 100.0)

    # Pouch counting (white) parameters
    LAB_L_MIN = cv2.getTrackbarPos("LAB_L_MIN", "Live Tuner")
    LAB_AB_TOL = cv2.getTrackbarPos("LAB_AB_TOL", "Live Tuner")
    DT_PEAK_REL = cv2.getTrackbarPos("DT_PEAK_REL x100", "Live Tuner") / 100.0

    # Force odd values >= 3 for morphology kernels
    WHITE_OPEN_K = max(3, cv2.getTrackbarPos("WHITE_OPEN_K", "Live Tuner") | 1)
    WHITE_CLOSE_K = max(3, cv2.getTrackbarPos("WHITE_CLOSE_K", "Live Tuner") | 1)

    TRAY_RIM_MARGIN = cv2.getTrackbarPos("TRAY_RIM_MARGIN", "Live Tuner")

    # Dominant color / white gate parameters
    WHITE_S_MAX = cv2.getTrackbarPos("WHITE_S_MAX", "Live Tuner")
    WHITE_V_MIN = cv2.getTrackbarPos("WHITE_V_MIN", "Live Tuner")
    WHITE_GATE_EXPANSION_FACTOR = cv2.getTrackbarPos("GATE_EXPAND x1000", "Live Tuner") / 1000.0

def print_current_values(args):
    """Print current parameter values for copying back to config.py"""
    print("\n" + "="*60)
    print("🎯 CURRENT TUNED PARAMETERS")
    print("="*60)
    print("# Tray Detection")
    print(f"TRAY_MIN_HEIGHT_DEFAULT = {args.tray_min}")
    print(f"TRAY_MAX_HEIGHT_DEFAULT = {args.tray_max}")
    print(f"SOLID_FRACTION_DEFAULT = {args.solid_frac:.3f}")
    print(f"GAP_BLUE_MAX_DEFAULT = {args.gap_blue_max:.3f}")
    print(f"BAND_FRACTION_DEFAULT = {args.band_frac:.3f}")
    print()
    print("# White-based Pouch Counting")
    print(f"LAB_L_MIN = {LAB_L_MIN}")
    print(f"LAB_AB_TOL = {LAB_AB_TOL}")
    print(f"DT_PEAK_REL = {DT_PEAK_REL:.3f}")
    print(f"WHITE_OPEN_K = {WHITE_OPEN_K}")
    print(f"WHITE_CLOSE_K = {WHITE_CLOSE_K}")
    print(f"TRAY_RIM_MARGIN = {TRAY_RIM_MARGIN}")
    print()
    print("# White Gate / Color Detection")
    print(f"WHITE_S_MAX = {WHITE_S_MAX}")
    print(f"WHITE_V_MIN = {WHITE_V_MIN}")
    print(f"WHITE_GATE_EXPANSION_FACTOR = {WHITE_GATE_EXPANSION_FACTOR:.4f}")
    print("="*60)

def reset_tuner_to_defaults(args):
    """Reset all trackbars to their default values"""
    # Import original defaults
    from config import (TRAY_MIN_HEIGHT_DEFAULT, TRAY_MAX_HEIGHT_DEFAULT,
                       SOLID_FRACTION_DEFAULT, GAP_BLUE_MAX_DEFAULT, BAND_FRACTION_DEFAULT)

    # Reset tray detection
    cv2.setTrackbarPos("tray_min", "Live Tuner", TRAY_MIN_HEIGHT_DEFAULT)
    cv2.setTrackbarPos("tray_max", "Live Tuner", TRAY_MAX_HEIGHT_DEFAULT)
    cv2.setTrackbarPos("solid_frac x100", "Live Tuner", int(SOLID_FRACTION_DEFAULT*100))
    cv2.setTrackbarPos("gap_blue_max x100", "Live Tuner", int(GAP_BLUE_MAX_DEFAULT*100))
    cv2.setTrackbarPos("band_frac x100", "Live Tuner", int(BAND_FRACTION_DEFAULT*100))

    # Reset white detection (use current config values as defaults)
    from config import (LAB_L_MIN as DEFAULT_LAB_L_MIN, LAB_AB_TOL as DEFAULT_LAB_AB_TOL,
                       DT_PEAK_REL as DEFAULT_DT_PEAK_REL, WHITE_OPEN_K as DEFAULT_WHITE_OPEN_K,
                       WHITE_CLOSE_K as DEFAULT_WHITE_CLOSE_K, TRAY_RIM_MARGIN as DEFAULT_TRAY_RIM_MARGIN,
                       WHITE_S_MAX as DEFAULT_WHITE_S_MAX, WHITE_V_MIN as DEFAULT_WHITE_V_MIN,
                       WHITE_GATE_EXPANSION_FACTOR as DEFAULT_WHITE_GATE_EXPANSION_FACTOR)

    cv2.setTrackbarPos("LAB_L_MIN", "Live Tuner", DEFAULT_LAB_L_MIN)
    cv2.setTrackbarPos("LAB_AB_TOL", "Live Tuner", DEFAULT_LAB_AB_TOL)
    cv2.setTrackbarPos("DT_PEAK_REL x100", "Live Tuner", int(DEFAULT_DT_PEAK_REL*100))
    cv2.setTrackbarPos("WHITE_OPEN_K", "Live Tuner", DEFAULT_WHITE_OPEN_K)
    cv2.setTrackbarPos("WHITE_CLOSE_K", "Live Tuner", DEFAULT_WHITE_CLOSE_K)
    cv2.setTrackbarPos("TRAY_RIM_MARGIN", "Live Tuner", DEFAULT_TRAY_RIM_MARGIN)
    cv2.setTrackbarPos("WHITE_S_MAX", "Live Tuner", DEFAULT_WHITE_S_MAX)
    cv2.setTrackbarPos("WHITE_V_MIN", "Live Tuner", DEFAULT_WHITE_V_MIN)
    cv2.setTrackbarPos("GATE_EXPAND x1000", "Live Tuner", int(DEFAULT_WHITE_GATE_EXPANSION_FACTOR*1000))

    print("🔄 Tuner reset to default values")

# Cached kernels for color detection
_color_kernels = {}

def get_color_kernel(size=None):
    """Get cached morphological kernel for color detection"""
    if size is None:
        size = COLOR_KERNEL_SIZE
    if size not in _color_kernels:
        _color_kernels[size] = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (size, size))
    return _color_kernels[size]

# ------------------------ Global caches for expensive operations ------------------------
_clahe_cache = None
_kernel_cache = {}

def get_clahe():
    """Cached CLAHE object"""
    global _clahe_cache
    if _clahe_cache is None:
        _clahe_cache = cv2.createCLAHE(CLAHE_CLIP_LIMIT, CLAHE_TILE_GRID_SIZE)
    return _clahe_cache

def get_kernel(shape, morph_type):
    """Cached morphological kernels"""
    key = (shape, morph_type)
    if key not in _kernel_cache:
        _kernel_cache[key] = cv2.getStructuringElement(morph_type, shape)
    return _kernel_cache[key]

# ------------------------ Optimized Color Detection Functions ------------------------

def _tray_roi_bounds(xL, xR, y_top, y_bot, W, H, margin=None):
    """Helper to get consistent tray ROI bounds for both cropping and drawing"""
    if margin is None:
        margin = ROI_MARGIN_DEFAULT
    x1 = max(0, xL - margin)
    x2 = min(W - 1, xR + margin)
    y1 = max(0, y_top - margin)
    y2 = min(H - 1, y_bot + margin)
    return x1, y1, x2, y2

def create_color_mask_fast(hsv_roi, color_ranges, exclude_mask=None, white_gate=None):
    """
    Fast color mask creation for ROI using optimized morphology
    Args:
        hsv_roi: HSV image of tray ROI
        color_ranges: Dictionary of color ranges
        exclude_mask: Mask of areas to exclude (e.g., tray plastic)
        white_gate: Mask of white pouch areas for gating color detection
    Returns:
        Dictionary of color masks
    """
    masks = {}
    kernel = get_color_kernel()  # Use default size from config
    
    for color_name, (lower, upper) in color_ranges.items():
        # Create mask
        mask = cv2.inRange(hsv_roi, np.array(lower, np.uint8), np.array(upper, np.uint8))
        
        # Special case: red composed of two ranges -> union into 'red'
        if color_name == "red1" or color_name == "red2":
            base = masks.get("red", None)
            mask = mask if base is None else cv2.bitwise_or(base, mask)
            color_name = "red"
        
        # Single morphological operation for noise reduction
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=MORPH_OPEN_ITERATIONS)
        # Gate by white pouch area (optional). 'white_gate' is already grown.
        if white_gate is not None:
            mask = cv2.bitwise_and(mask, white_gate)
        # Exclude tray plastic areas
        if exclude_mask is not None:
            mask = cv2.bitwise_and(mask, mask, mask=cv2.bitwise_not(exclude_mask))
        
        masks[color_name] = mask
    
    return masks

def count_pouches_from_white(bgr_roi):
    """
    Count pouches based on white pouch cores using LAB color space and watershed splitting.

    Args:
        bgr_roi: BGR image of detected tray region

    Returns:
        tuple: (centers, count, debug_mask)
            - centers: List of (cx, cy) pouch center coordinates
            - count: Number of detected pouches
            - debug_mask: White mask for debugging (uint8)
    """
    if bgr_roi.shape[0] < 20 or bgr_roi.shape[1] < 20:
        return [], 0, np.zeros((bgr_roi.shape[0], bgr_roi.shape[1]), np.uint8)

    H, W = bgr_roi.shape[:2]

    # 1) Whiteness mask (LAB + HSV) --------------------------
    lab = cv2.cvtColor(bgr_roi, cv2.COLOR_BGR2LAB)
    L, A, B = cv2.split(lab)
    clahe = cv2.createCLAHE(2.0, (8,8))
    L = clahe.apply(L)

    # LAB neutral color detection (a and b near 128 = neutral)
    ab_neutral = (np.abs(A.astype(np.int16) - 128) <= LAB_AB_TOL) & \
                 (np.abs(B.astype(np.int16) - 128) <= LAB_AB_TOL)
    lab_white = (L >= LAB_L_MIN) & ab_neutral

    # HSV white detection (existing logic)
    hsv = cv2.cvtColor(bgr_roi, cv2.COLOR_BGR2HSV)
    hsv_white = cv2.inRange(hsv,
                            (0, 0, WHITE_V_MIN),
                            (179, WHITE_S_MAX, 255)).astype(bool)

    # Combine LAB and HSV white detection
    #white = (lab_white & hsv_white)
    white = lab_white

    # 2) Exclude blue rim near borders (reuse existing logic) ----
    tray_blue = cv2.inRange(hsv,
                            np.array(TRAY_BLUE_RANGE[0], np.uint8),
                            np.array(TRAY_BLUE_RANGE[1], np.uint8)).astype(bool)
    rim = np.zeros((H, W), np.uint8)
    m = TRAY_RIM_MARGIN
    rim[:m, :] = rim[-m:, :] = rim[:, :m] = rim[:, -m:] = 255
    exclude = (tray_blue & (rim > 0))
    white[exclude] = False

    # 3) Clean mask ------------------------------------------
    k_open = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (WHITE_OPEN_K, WHITE_OPEN_K))
    k_close = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (WHITE_CLOSE_K, WHITE_CLOSE_K))
    white_u8 = (white.astype(np.uint8) * 255)
    white_u8 = cv2.morphologyEx(white_u8, cv2.MORPH_OPEN, k_open, iterations=1)
    white_u8 = cv2.morphologyEx(white_u8, cv2.MORPH_CLOSE, k_close, iterations=1)

    # 4) Distance-transform markers --------------------------
    dist = cv2.distanceTransform(white_u8, cv2.DIST_L2, 5)
    if dist.max() == 0:  # No white areas found
        return [], 0, white_u8

    _, peaks = cv2.threshold(dist, DT_PEAK_REL * dist.max(), 255, cv2.THRESH_BINARY)
    peaks = peaks.astype(np.uint8)
    num_markers, markers = cv2.connectedComponents(peaks)
    markers = markers + 1  # 0 is reserved for background
    markers[white_u8 == 0] = 0

    # 5) Watershed split -------------------------------------
    # Watershed expects 3-channel image; use original ROI
    ws_img = bgr_roi.copy()
    cv2.watershed(ws_img, markers)  # In-place labeling

    # 6) Collect components ----------------------------------
    roi_area = H * W
    min_area = max(MIN_POUCH_AREA_ABSOLUTE, roi_area * MIN_POUCH_AREA_FRACTION)
    max_area = roi_area * MAX_POUCH_AREA_FRACTION

    centers = []
    labels = np.unique(markers)
    for lbl in labels:
        if lbl <= 1:  # 0 = background, 1 = unknown rim from watershed
            continue
        mask = (markers == lbl)
        area = int(mask.sum())
        if area < min_area or area > max_area:
            continue
        ys, xs = np.nonzero(mask)
        if xs.size == 0:
            continue
        cx = int(xs.mean())
        cy = int(ys.mean())
        centers.append((cx, cy))

    return centers, len(centers), white_u8

def detect_pouches_and_colors_fast(bgr_roi):
    """
    Fast pouch detection and color analysis within tray ROI.
    Now includes both white-based and color-based counting for comparison.

    Args:
        bgr_roi: BGR image of detected tray region
    Returns:
        tuple: (color_counts, color_pouches, white_pouches, dominant_color, color_centers, white_centers)
            - color_counts: Counter of pouches by color
            - color_pouches: Total pouches from color-based detection
            - white_pouches: Total pouches from white-based detection
            - dominant_color: Most common color from color detection
            - color_centers: List of (cx, cy, color) from color detection
            - white_centers: List of (cx, cy) from white detection
    """
    if bgr_roi.shape[0] < 20 or bgr_roi.shape[1] < 20:
        return {}, 0, 0, "none", [], []
    
    # NEW: White-based counting first
    white_centers, white_count, white_debug_mask = count_pouches_from_white(bgr_roi)

    # Convert to HSV for color detection
    hsv_roi = cv2.cvtColor(bgr_roi, cv2.COLOR_BGR2HSV)
    H, W = hsv_roi.shape[:2]

    # Build an EXCLUDE mask: tray plastic blue near the ROI borders
    tray_blue = cv2.inRange(hsv_roi,
                            np.array(TRAY_BLUE_RANGE[0], np.uint8),
                            np.array(TRAY_BLUE_RANGE[1], np.uint8))
    rim = np.zeros((H, W), np.uint8)
    m = TRAY_RIM_MARGIN
    rim[:m, :] = 255
    rim[-m:, :] = 255
    rim[:, :m] = 255
    rim[:, -m:] = 255
    exclude_mask = cv2.bitwise_and(tray_blue, rim)  # only tray-blue near the edges
    # --- WHITE GATE (pouches are white background) ---
    white_core = cv2.inRange(
        hsv_roi,
        np.array([0,   0, WHITE_V_MIN], np.uint8),
        np.array([179, WHITE_S_MAX, 255], np.uint8)
    )
    # clean small holes, then grow to cover colored print adjacent to white
    white_core = cv2.morphologyEx(white_core, cv2.MORPH_CLOSE, get_color_kernel(), iterations=MORPH_CLOSE_ITERATIONS)
    gate_px = max(4, int(WHITE_GATE_EXPANSION_FACTOR * min(H, W)))  # Configurable % of shorter tray dimension
    gate_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2*gate_px+1, 2*gate_px+1))
    near_white = cv2.dilate(white_core, gate_kernel, iterations=1)
    
    # Get color masks
    color_masks = create_color_mask_fast(
        hsv_roi, COLOR_RANGES,
        exclude_mask=exclude_mask,
        white_gate=near_white
    )
    
    # Calculate color percentages and find contours
    roi_area = bgr_roi.shape[0] * bgr_roi.shape[1]
    
    def _accumulate_from_masks(masks):
        color_percentages = {}
        pouch_centers = []
        total_pouches = 0
        color_counts = Counter()
        for color_name, mask in masks.items():
            color_pixels = cv2.countNonZero(mask)
            color_percentages[color_name] = (color_pixels / roi_area) * 100
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            min_pouch_area = max(MIN_POUCH_AREA_ABSOLUTE, roi_area * MIN_POUCH_AREA_FRACTION)
            max_pouch_area = roi_area * MAX_POUCH_AREA_FRACTION
            valid_contours = [cnt for cnt in contours if min_pouch_area <= cv2.contourArea(cnt) <= max_pouch_area]
            for cnt in valid_contours:
                M = cv2.moments(cnt)
                if M["m00"] > 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    pouch_centers.append((cx, cy, color_name))
            total_pouches += len(valid_contours)
        color_counts.update([c for _, _, c in pouch_centers])
        return color_counts, total_pouches, pouch_centers

    color_counts, color_pouches, color_centers = _accumulate_from_masks(color_masks)

    # Fail-safe: if nothing detected (harsh lighting / too strict gate), retry once without white gating
    if color_pouches == 0:
        ungated_masks = create_color_mask_fast(
            hsv_roi, COLOR_RANGES,
            exclude_mask=exclude_mask,
            white_gate=None
        )
        color_counts, color_pouches, color_centers = _accumulate_from_masks(ungated_masks)

    # Dominant color by COUNT of pouch blobs (not pixel %)
    dominant_color = "none"
    if color_pouches:
        dominant_color = max(color_counts.items(), key=lambda kv: kv[1])[0] if len(color_counts) else "none"

    # Return both color-based and white-based results
    return color_counts, color_pouches, white_count, dominant_color, color_centers, white_centers

def draw_pouch_overlays_fast(bgr, trays, band, color_detection_enabled=True):
    """
    Draw pouch detection overlays on detected trays
    Args:
        bgr: Original BGR frame
        trays: List of detected trays
        band: Detection band (xL, xR)
        color_detection_enabled: Whether to perform color detection
    Returns:
        Annotated frame with pouch overlays
    """
    if not color_detection_enabled or not trays:
        return bgr
    
    result = bgr.copy()
    xL, xR = band
    
    # Color map for visualization from config
    color_map = POUCH_VISUALIZATION_COLORS.copy()
    color_map["none"] = (128, 128, 128)  # Gray for undetected
    
    for tray_idx, (y_top, y_bot, dist, gapQ) in enumerate(trays):
        # Extract tray ROI with margin
        crop_x1 = max(0, xL - ROI_MARGIN_DEFAULT)
        crop_x2 = min(bgr.shape[1], xR + ROI_MARGIN_DEFAULT)
        crop_y1 = max(0, y_top - ROI_MARGIN_DEFAULT)
        crop_y2 = min(bgr.shape[0], y_bot + ROI_MARGIN_DEFAULT)
        
        tray_roi = bgr[crop_y1:crop_y2, crop_x1:crop_x2]
        
        # Perform color detection using both methods
        color_counts, color_pouches, white_pouches, dominant_color, color_centers, white_centers = detect_pouches_and_colors_fast(tray_roi)

        # Draw color-based pouch centers and labels
        for px, py, color in color_centers:
            # Convert local coordinates to global coordinates
            global_x = crop_x1 + px
            global_y = crop_y1 + py

            # Draw color pouch marker
            color_bgr = color_map.get(color, (128, 128, 128))
            cv2.circle(result, (global_x, global_y), 6, color_bgr, -1)
            cv2.circle(result, (global_x, global_y), 8, (255, 255, 255), 2)

        # Draw white-based pouch centers with different style
        for px, py in white_centers:
            # Convert local coordinates to global coordinates
            global_x = crop_x1 + px
            global_y = crop_y1 + py

            # Draw white pouch marker (white center with black border)
            cv2.circle(result, (global_x, global_y), 4, (255, 255, 255), -1)
            cv2.circle(result, (global_x, global_y), 6, (0, 0, 0), 2)

        # Draw color summary for this tray showing both counts
        if color_pouches > 0 or white_pouches > 0:
            summary_x = crop_x1 + 5
            summary_y = crop_y1 + 30

            # Background for text (adjusted height for both counts)
            text_height = 35 + len(color_counts) * 15
            cv2.rectangle(result, (summary_x - 2, summary_y - 18),
                         (summary_x + 220, summary_y + text_height), (0, 0, 0), -1)
            cv2.rectangle(result, (summary_x - 2, summary_y - 18),
                         (summary_x + 220, summary_y + text_height), (255, 255, 255), 1)

            # Summary text showing both counts
            cv2.putText(result, f"Color: {color_pouches} | White: {white_pouches}",
                       (summary_x, summary_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(result, f"Dominant: {dominant_color}",
                       (summary_x, summary_y + 15), cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                       color_map.get(dominant_color, (255, 255, 255)), 1)

            # Individual color counts
            y_offset = 30
            for color, count in color_counts.items():
                cv2.putText(result, f"{color}: {count}",
                           (summary_x, summary_y + y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4,
                           color_map.get(color, (255, 255, 255)), 1)
                y_offset += 15
    
    return result

# ------------------------ Color / Segmentation (Optimized) ------------------------

def blue_mask_fast(bgr):
    """Optimized HSV threshold with reduced morphology operations."""
    hsv = cv2.cvtColor(bgr, cv2.COLOR_BGR2HSV)
    mask = cv2.inRange(hsv, (90,60,40), (140,255,255))
    
    # Reduced morphology - single operation instead of multiple
    kernel = get_kernel((5,5), cv2.MORPH_RECT)
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=1)
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, get_kernel((7,7), cv2.MORPH_RECT), iterations=1)
    
    return mask

# ------------------------ Vectorized 1D profile computation ------------------------

def longest_blue_runs_vectorized(mask, band_frac=0.6, smooth_win=21):
    """Vectorized version of longest blue runs computation."""
    h, w = mask.shape
    band_frac = np.clip(band_frac, 0.2, 0.95)
    xL = int((1.0 - band_frac) * 0.5 * w)
    xR = int((1.0 + band_frac) * 0.5 * w)
    central = (mask[:, xL:xR] > 0).astype(np.uint8)

    # Vectorized computation using numpy operations
    padded = np.pad(central, ((0,0), (1,1)), 'constant')
    diffs = np.diff(padded.astype(np.int16), axis=1)
    
    # Find run lengths efficiently
    runs = np.zeros(h, dtype=np.int32)
    for y in range(h):
        row_diffs = diffs[y]
        starts = np.where(row_diffs == 1)[0]
        ends = np.where(row_diffs == -1)[0]
        if starts.size > 0 and ends.size > 0:
            lens = ends - starts
            runs[y] = int(lens.max()) if lens.size > 0 else 0

    # Optimized convolution
    if smooth_win > 1:
        k = np.ones(smooth_win, np.float32) / float(smooth_win)
        smooth = np.convolve(runs.astype(np.float32), k, mode="same")
    else:
        smooth = runs.astype(np.float32)
    
    return runs, smooth, (xL, xR)

def fuse_profile_with_hough_fast(bgr, smooth, xL, xR, width_frac_thr=0.35, hough_len_frac=0.45, tol=12):
    """Optimized fusion with cached operations."""
    h, w = bgr.shape[:2]
    thr = int((xR - xL) * width_frac_thr)
    cand_rows = np.where(smooth >= thr)[0]

    bands = []
    if cand_rows.size > 0:
        # More efficient grouping
        diff_mask = np.diff(cand_rows) > 10
        if diff_mask.any():
            groups = np.split(cand_rows, np.where(diff_mask)[0] + 1)
        else:
            groups = [cand_rows]
        
        bands = [int(np.mean(g)) for g in groups]

    # Optimized edge detection
    B = bgr[:,:,0]
    clahe = get_clahe()
    enhanced = clahe.apply(B)
    
    # Single-pass Sobel with optimized parameters
    Sob = cv2.Sobel(enhanced, cv2.CV_16S, 0, 1, ksize=3)
    Sob = cv2.convertScaleAbs(Sob)
    
    # Skip Gaussian blur for speed
    _, edge = cv2.threshold(Sob, 34, 255, cv2.THRESH_BINARY)
    edge = cv2.morphologyEx(edge, cv2.MORPH_CLOSE, get_kernel((5,5), cv2.MORPH_RECT), iterations=1)

    # Optimized Hough transform
    min_len = int(w * hough_len_frac)
    lines = cv2.HoughLinesP(edge, 1, np.pi/180, threshold=60, minLineLength=min_len, maxLineGap=25)
    
    hough_ys = []
    if lines is not None:
        # Vectorized horizontal line filtering
        y_diffs = np.abs(lines[:,0,1] - lines[:,0,3])
        horizontal_mask = y_diffs <= 3
        if horizontal_mask.any():
            horizontal_lines = lines[horizontal_mask]
            hough_ys = ((horizontal_lines[:,0,1] + horizontal_lines[:,0,3]) // 2).tolist()

    # Efficient fusion
    fused = []
    if hough_ys:
        hough_ys_array = np.array(hough_ys)
        for y in bands:
            dists = np.abs(hough_ys_array - y)
            min_idx = np.argmin(dists)
            best = hough_ys_array[min_idx]
            fused.append(int(0.5*(best + y)) if dists[min_idx] <= tol else y)
    else:
        fused = bands
    
    return sorted(fused), edge

# ------------------------ Optimized Length + Gap Detection ------------------------

def detect_trays_by_length_gap_fast(mask, xL, xR, len_px_range=(250,430), solid_frac=0.45, gap_blue_max=0.30):
    """Optimized tray detection with vectorized operations."""
    h, w = mask.shape
    central = (mask[:, xL:xR] > 0).astype(np.uint8)
    runs, smooth, _ = longest_blue_runs_vectorized(mask, band_frac=(xR-xL)/w, smooth_win=21)

    band_thr = int((xR - xL) * solid_frac)
    band_rows = np.where(smooth >= band_thr)[0]
    
    bands = []
    if band_rows.size > 0:
        diff_mask = np.diff(band_rows) > 12
        if diff_mask.any():
            groups = np.split(band_rows, np.where(diff_mask)[0] + 1)
        else:
            groups = [band_rows]
        
        bands = [(int(np.mean(g)), int(g[0]), int(g[-1])) for g in groups]

    # Vectorized tray detection
    trays = []
    if len(bands) >= 2:
        for i in range(len(bands)):
            for j in range(i+1, len(bands)):
                y1c, y1s, y1e = bands[i]
                y2c, y2s, y2e = bands[j]
                dist = abs(y2c - y1c)
                
                if len_px_range[0] <= dist <= len_px_range[1]:
                    y_top = min(y1c, y2c)
                    y_bot = max(y1c, y2c)
                    gap_s = min(y1e, y2e)
                    gap_e = max(y1s, y2s)

                    if gap_s >= gap_e:
                        gap_blue_fraction = 0.0
                    else:
                        gap = central[gap_s:gap_e, :]
                        gap_blue_fraction = 0.0 if gap.size == 0 else float((gap.mean(axis=1) >= 0.30).mean())

                    if gap_blue_fraction <= gap_blue_max:
                        trays.append((y_top, y_bot, dist, gap_blue_fraction))

    # Efficient sorting and merging
    if trays:
        target_dist = (len_px_range[0] + len_px_range[1]) / 2
        trays.sort(key=lambda t: (abs(target_dist - t[2]), t[3]))
        
        merged = [trays[0]]
        for t in trays[1:]:
            if abs(t[0] - merged[-1][0]) > 20:
                merged.append(t)
        trays = merged

    return trays, bands, runs, smooth

# ------------------------ Optimized Visualization ------------------------

def draw_overlay_fast(bgr, trays, bands, band, overlay_type="len_gap", prev_lines=None, edge=None, counter=None, color_detection=True):
    """Fast overlay drawing with clean tray visualization (no text overlay on video)."""
    out = bgr.copy()
    h, w = out.shape[:2]
    xL, xR = band
    
    # Draw detection area
    cv2.rectangle(out, (xL,0), (xR,h-1), UI_COLOR_WHITE, 1)
    
    # Use colors from config for alternating trays
    tray_colors = TRAY_BOUNDARY_COLORS
    
    # Store color detection results for side panel
    tray_color_info = []
    
    if overlay_type == "len_gap":
        # Draw bands (detection zones)
        for (yc, ys, ye) in bands:
            cv2.line(out, (0, ys), (w-1, ys), (255,120,0), 1)
            cv2.line(out, (0, ye), (w-1, ye), (255,120,0), 1)
        
        # Draw trays with clean bounding boxes (no text overlay)
        for k, (y_top, y_bot, dist, gapQ) in enumerate(trays):
            color = tray_colors[k % len(tray_colors)]
            
            # Draw horizontal lines for tray boundaries
            cv2.line(out, (0, y_top), (w-1, y_top), color, 2)
            cv2.line(out, (0, y_bot), (w-1, y_bot), color, 2)
            
            # Draw bounding rectangle using consistent bounds
            rect_x1, rect_y1, rect_x2, rect_y2 = _tray_roi_bounds(xL, xR, y_top, y_bot, w, h)
            cv2.rectangle(out, (rect_x1, rect_y1), (rect_x2, rect_y2), color, 3)
            
            # Draw corner markers for better visibility
            corner_size = CORNER_MARKER_SIZE
            # Top-left corner
            cv2.line(out, (rect_x1, rect_y1), (rect_x1 + corner_size, rect_y1), color, CORNER_MARKER_THICKNESS)
            cv2.line(out, (rect_x1, rect_y1), (rect_x1, rect_y1 + corner_size), color, CORNER_MARKER_THICKNESS)
            # Top-right corner  
            cv2.line(out, (rect_x2, rect_y1), (rect_x2 - corner_size, rect_y1), color, CORNER_MARKER_THICKNESS)
            cv2.line(out, (rect_x2, rect_y1), (rect_x2, rect_y1 + corner_size), color, CORNER_MARKER_THICKNESS)
            # Bottom-left corner
            cv2.line(out, (rect_x1, rect_y2), (rect_x1 + corner_size, rect_y2), color, CORNER_MARKER_THICKNESS)
            cv2.line(out, (rect_x1, rect_y2), (rect_x1, rect_y2 - corner_size), color, CORNER_MARKER_THICKNESS)
            # Bottom-right corner
            cv2.line(out, (rect_x2, rect_y2), (rect_x2 - corner_size, rect_y2), color, CORNER_MARKER_THICKNESS)
            cv2.line(out, (rect_x2, rect_y2), (rect_x2, rect_y2 - corner_size), color, CORNER_MARKER_THICKNESS)
            
            # Perform color detection for this tray if enabled
            color_info = {"tray_num": k+1, "dist": dist, "pouches": 0, "dominant": "none", "colors": {}}
            
            if color_detection:
                # Extract tray ROI using consistent bounds
                crop_x1, crop_y1, crop_x2, crop_y2 = _tray_roi_bounds(xL, xR, y_top, y_bot, w, h)

                tray_roi = bgr[crop_y1:crop_y2, crop_x1:crop_x2].copy()

                if tray_roi.shape[0] > 20 and tray_roi.shape[1] > 20:
                    # Detect pouches using both methods
                    color_counts, color_pouches, white_pouches, dominant_color, color_centers, white_centers = detect_pouches_and_colors_fast(tray_roi)

                    # Update color info for side panel (include both counts)
                    color_info.update({
                        "color_pouches": color_pouches,
                        "white_pouches": white_pouches,
                        "dominant": dominant_color,
                        "colors": dict(color_counts)
                    })

                    # Draw color-based pouch markers
                    color_map = POUCH_VISUALIZATION_COLORS.copy()
                    color_map["none"] = (128, 128, 128)  # Gray for undetected

                    for px, py, pouch_color in color_centers:
                        # Map to global and CLIP to ROI (prevents markers spilling out)
                        global_x = np.clip(crop_x1 + px, crop_x1, crop_x2)
                        global_y = np.clip(crop_y1 + py, crop_y1, crop_y2)
                        pouch_bgr = color_map.get(pouch_color, (128, 128, 128))
                        cv2.circle(out, (global_x, global_y), 4, pouch_bgr, -1)
                        cv2.circle(out, (global_x, global_y), 6, UI_COLOR_WHITE, 1)

                    # Draw white-based pouch markers with different style
                    for px, py in white_centers:
                        # Map to global and CLIP to ROI
                        global_x = np.clip(crop_x1 + px, crop_x1, crop_x2)
                        global_y = np.clip(crop_y1 + py, crop_y1, crop_y2)
                        # White markers: white center with black border
                        cv2.circle(out, (global_x, global_y), 3, (255, 255, 255), -1)
                        cv2.circle(out, (global_x, global_y), 5, (0, 0, 0), 1)
            
            # Add tray number for counter tracking
            if counter is not None and counter.current_tray_id is not None:
                color_info["tray_num"] = int(counter.current_tray_id.split('_')[1]) if counter.current_tray_id else k+1
            
            tray_color_info.append(color_info)
    
    elif overlay_type == "prev_lines" and prev_lines is not None:
        # Draw previous logic lines with bounding boxes
        for i, y in enumerate(prev_lines):
            color = tray_colors[i % len(tray_colors)]
            cv2.line(out, (0,y), (w-1,y), color, 2)
            
            # Create bounding box for line-based detection
            box_height = 40  # Estimated tray height for line detection
            y_top = max(0, y - box_height//2)
            y_bot = min(h-1, y + box_height//2)
            
            rect_x1 = max(0, xL - 10)
            rect_x2 = min(w-1, xR + 10)
            cv2.rectangle(out, (rect_x1, y_top), (rect_x2, y_bot), color, 3)
        
        # Show edge detection in corner if available
        if edge is not None:
            e_small = cv2.resize(cv2.cvtColor(edge, cv2.COLOR_GRAY2BGR), (w//4, h//4))
            out[5:5+e_small.shape[0], w - e_small.shape[1]-5: w-5] = e_small
    
    return out, tray_color_info


def put_label(img, text, org, fg=None, bg=None, scale=None, thick=None, pad=None):
    """Helper function to draw text with background chip and subtle shadow for modern appearance"""
    if fg is None: fg = LABEL_FG_COLOR_DEFAULT
    if bg is None: bg = LABEL_BG_COLOR_DEFAULT  
    if scale is None: scale = LABEL_FONT_SCALE_DEFAULT
    if thick is None: thick = LABEL_THICKNESS_DEFAULT
    if pad is None: pad = LABEL_PADDING_DEFAULT
    
    (tw, th), bl = cv2.getTextSize(text, cv2.FONT_HERSHEY_DUPLEX, scale, thick)
    x, y = org
    cv2.rectangle(img, (x- pad, y- th- pad), (x+ tw+ pad, y+ bl+ pad), bg, -1, cv2.LINE_AA)
    # subtle shadow
    cv2.putText(img, text, (x+1, y+1), cv2.FONT_HERSHEY_DUPLEX, scale, (0,0,0), thick, cv2.LINE_AA)
    cv2.putText(img, text, (x,   y),   cv2.FONT_HERSHEY_DUPLEX, scale, fg,       thick, cv2.LINE_AA)


def create_side_panel_layout(overlay, mask, tray_color_info, counter=None, args=None, color_detection=True, debug_mode=0):
    """
    Create a side panel layout with video on left and debug info on right
    Args:
        overlay: Main video overlay
        mask: Detection mask
        tray_color_info: List of tray color information
        counter: GateCounter instance
        args: Command line arguments
        color_detection: Whether color detection is enabled
        debug_mode: Current debug mode
    Returns:
        Combined layout with side panel
    """
    h, w = overlay.shape[:2]
    
    # Create the side panel using config settings
    panel = np.zeros((h, PANEL_WIDTH, 3), dtype=np.uint8)
    panel.fill(PANEL_BACKGROUND_COLOR)  # Dark gray background
    
    # Text settings from config
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = FONT_SCALE_NORMAL
    font_thickness = FONT_THICKNESS
    line_height = LINE_HEIGHT
    margin = MARGIN_DEFAULT
    
    y_pos = margin + 20
    
    # Header using config colors
    put_label(panel, "DETECTION PANEL", (margin, y_pos), fg=UI_COLOR_WHITE, bg=(30,30,30), scale=FONT_SCALE_HEADER, thick=FONT_THICKNESS)
    y_pos += HEADER_SPACING
    
    # Status Information
    debug_names = ["LEN+GAP", "LEN+GAP+Mask", "Prev+Lines", "Prev+Mask", "Split"]
    mode_name = debug_names[min(debug_mode, len(debug_names)-1)]
    
    status_lines = [
        f"Mode: {debug_mode} ({mode_name})",
        f"Trays Detected: {len(tray_color_info)}",
        f"Color Detection: {'ON' if color_detection else 'OFF'}",
        f"Auto Crop: {'ON' if args and args.save_tray_crops else 'OFF'}"
    ]
    
    if counter:
        status_lines.extend([
            f"Gate State: {counter.state}",
            f"Total Count: {counter.total}"
        ])
    
    for line in status_lines:
        cv2.putText(panel, line, (margin, y_pos), font, font_scale, (255, 255, 255), font_thickness)
        y_pos += line_height
    
    y_pos += 15  # Extra spacing
    
    # Tray Information
    if tray_color_info:
        cv2.putText(panel, "TRAY DETAILS:", (margin, y_pos), font, 0.5, (0, 255, 255), font_thickness + 1)
        y_pos += 25
        
        for i, tray_info in enumerate(tray_color_info):
            if y_pos > h - 100:  # Prevent overflow
                cv2.putText(panel, "... (more trays)", (margin, y_pos), font, font_scale, (128, 128, 128), font_thickness)
                break
                
            # Tray header with color coding from config
            tray_color = TRAY_BOUNDARY_COLORS[i % len(TRAY_BOUNDARY_COLORS)]
            
            tray_line = f"TRAY #{tray_info['tray_num']}: {tray_info['dist']}px"
            cv2.putText(panel, tray_line, (margin, y_pos), font, font_scale, tray_color, font_thickness + 1)
            y_pos += line_height
            
            if color_detection and (tray_info.get('color_pouches', 0) > 0 or tray_info.get('white_pouches', 0) > 0):
                # Pouch summary showing both counts
                color_count = tray_info.get('color_pouches', 0)
                white_count = tray_info.get('white_pouches', 0)
                dominant = tray_info.get('dominant', 'none')

                pouch_line = f"  Color: {color_count} | White: {white_count}"
                cv2.putText(panel, pouch_line, (margin + 10, y_pos), font, font_scale - 0.05, (255, 255, 255), font_thickness)
                y_pos += line_height

                # Show dominant color
                if dominant != 'none':
                    dom_line = f"  Dominant: {dominant}"
                    cv2.putText(panel, dom_line, (margin + 10, y_pos), font, font_scale - 0.05, (200, 200, 200), font_thickness)
                    y_pos += line_height

                # Color breakdown using config color map
                colors = tray_info.get('colors', {})
                for color_name, count in colors.items():
                    if count > 0:
                        color_bgr = POUCH_VISUALIZATION_COLORS.get(color_name, UI_COLOR_WHITE)
                        color_line = f"    {color_name}: {count}"
                        cv2.putText(panel, color_line, (margin + 20, y_pos), font, font_scale - 0.1, color_bgr, font_thickness)
                        y_pos += line_height - 2
            
            y_pos += 5  # Spacing between trays
    else:
        cv2.putText(panel, "No trays detected", (margin, y_pos), font, font_scale, UI_COLOR_GRAY, font_thickness)
        y_pos += line_height + 10
    
    # Controls section at bottom
    controls_start_y = h - 120
    cv2.putText(panel, "CONTROLS:", (margin, controls_start_y), font, 0.5, (0, 255, 255), font_thickness + 1)
    
    control_lines = [
        "q/ESC: Quit",
        "SPACE: Pause/Resume", 
        "d: Debug Mode",
        "x: Toggle Color",
        "t: Toggle Crop",
        "s: Save Frame"
    ]
    
    for i, control in enumerate(control_lines):
        cv2.putText(panel, control, (margin, controls_start_y + 20 + i * 12), font, 0.35, (200, 200, 200), font_thickness)
    
    # Add mini mask view in panel
    if mask is not None and debug_mode in [0, 2]:  # Show mini mask for main modes
        mask_h = 80
        mask_w = int((mask.shape[1] / mask.shape[0]) * mask_h)
        mask_small = cv2.resize(mask, (mask_w, mask_h))
        mask_bgr = cv2.cvtColor(mask_small, cv2.COLOR_GRAY2BGR)
        
        # Position in panel
        mask_y = h - mask_h - 10
        mask_x = PANEL_WIDTH - mask_w - MARGIN_DEFAULT
        
        panel[mask_y:mask_y + mask_h, mask_x:mask_x + mask_w] = mask_bgr
        put_label(panel, "Mask", (mask_x, mask_y - 6), fg=(255,255,255), bg=(0,0,0), scale=0.45)
    
    # Combine overlay and panel
    combined = np.hstack([overlay, panel])
    
    return combined

def create_debug_view_fast(overlay, mask, debug_mode=0, tray_color_info=None, counter=None, args=None, color_detection=True):
    """Enhanced debug view creation with side panel layout."""
    h, w = overlay.shape[:2]
    
    if debug_mode == 0:  # Main LEN+GAP view with side panel
        return create_side_panel_layout(overlay, mask, tray_color_info or [], counter, args, color_detection, debug_mode)
        
    elif debug_mode == 1:  # LEN+GAP with side mask and panel
        # Create side-by-side mask view
        mask_width = min(w//3, h)
        mask_resized = cv2.resize(mask, (mask_width, h))
        mask_bgr = cv2.cvtColor(mask_resized, cv2.COLOR_GRAY2BGR)
        
        overlay_width = w - mask_width
        overlay_resized = cv2.resize(overlay, (overlay_width, h))
        
        video_with_mask = np.hstack([overlay_resized, mask_bgr])
        return create_side_panel_layout(video_with_mask, None, tray_color_info or [], counter, args, color_detection, debug_mode)
        
    elif debug_mode == 2:  # Previous logic view with side panel
        return create_side_panel_layout(overlay, mask, tray_color_info or [], counter, args, color_detection, debug_mode)
        
    elif debug_mode == 3:  # Previous logic with side mask and panel
        mask_width = min(w//3, h)
        mask_resized = cv2.resize(mask, (mask_width, h))
        mask_bgr = cv2.cvtColor(mask_resized, cv2.COLOR_GRAY2BGR)
        
        overlay_width = w - mask_width
        overlay_resized = cv2.resize(overlay, (overlay_width, h))
        
        video_with_mask = np.hstack([overlay_resized, mask_bgr])
        return create_side_panel_layout(video_with_mask, None, tray_color_info or [], counter, args, color_detection, debug_mode)
        
    else:  # Mode 4: Split screen with side panel
        overlay_small = cv2.resize(overlay, (w, h//2))
        mask_bgr = cv2.cvtColor(cv2.resize(mask, (w, h//2)), cv2.COLOR_GRAY2BGR)
        video_split = np.vstack([overlay_small, mask_bgr])
        return create_side_panel_layout(video_split, None, tray_color_info or [], counter, args, color_detection, debug_mode)

# ------------------------ Tray ROI Cropping and Saving ------------------------

def save_tray_crops(original_frame, trays, band, outdir, frame_idx=None):
    """
    Crop and save each detected tray as a separate image.
    
    Args:
        original_frame: Original BGR frame
        trays: List of (y_top, y_bot, dist, gapQ) tuples
        band: (xL, xR) detection band coordinates
        outdir: Output directory path
        frame_idx: Optional frame index for naming
    
    Returns:
        List of saved file paths
    """
    global GLOBAL_TRAY_COUNTER
    
    # Create tray_images directory
    tray_dir = Path(outdir) / "tray_images"
    original_dir = Path(outdir) / "original_tray_images"
    tray_dir.mkdir(parents=True, exist_ok=True)
    original_dir.mkdir(parents=True, exist_ok=True)

    saved_files = []
    h, w = original_frame.shape[:2]
    xL, xR = band
    
    for k, (y_top, y_bot, dist, gapQ) in enumerate(trays):
        # Use consistent bounds for cropping
        crop_x1, crop_y1, crop_x2, crop_y2 = _tray_roi_bounds(xL, xR, y_top, y_bot, w, h, margin=20)
        
        # Crop the tray region
        tray_crop = original_frame[crop_y1:crop_y2, crop_x1:crop_x2]
        
        # Skip if crop is too small
        if tray_crop.shape[0] < 20 or tray_crop.shape[1] < 20:
            continue
        
        # Generate filename with timestamp and global counter
        timestamp = int(time.time() * 1000)
        if frame_idx is not None:
            filename = f"tray_{GLOBAL_TRAY_COUNTER:04d}_frame_{frame_idx:04d}_{timestamp}.png"
        else:
            filename = f"tray_{GLOBAL_TRAY_COUNTER:04d}_{timestamp}.png"
        

        file_path = original_dir / 'original_' + filename

        success = cv2.imwrite(str(file_path), tray_crop)
        print(f"Saved original tray crop: {'original_' + filename} (Size: {tray_crop.shape[1]}x{tray_crop.shape[0]})")


        file_path = tray_dir / filename
        
        # Add info overlay to the crop
        info_crop = tray_crop.copy()
        info_text = f"Tray #{GLOBAL_TRAY_COUNTER} | {dist}px | Gap:{gapQ:.2f}"
        
        # Add semi-transparent background for text
        overlay = info_crop.copy()
        cv2.rectangle(overlay, (5, 5), (info_crop.shape[1]-5, 35), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, info_crop, 0.3, 0, info_crop)
        
        # Add text
        cv2.putText(info_crop, info_text, (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.5, 
                   (0, 255, 255), 1, cv2.LINE_AA)
        
        # Save the cropped tray
        success = cv2.imwrite(str(file_path), info_crop)
        
        if success:
            saved_files.append(str(file_path))
            print(f"Saved tray crop: {filename} (Size: {tray_crop.shape[1]}x{tray_crop.shape[0]})")
        else:
            print(f"Failed to save: {filename}")
        
        GLOBAL_TRAY_COUNTER += 1
    
    return saved_files

def save_tray_crops_once_per_detection(original_frame, trays, band, outdir, counter, frame_idx=None):
    """
    Save tray crops only once per tray detection (when tray first enters gate).
    
    Args:
        original_frame: Original BGR frame
        trays: List of (y_top, y_bot, dist, gapQ) tuples
        band: (xL, xR) detection band coordinates
        outdir: Output directory path
        counter: GateCounter instance to track tray state
        frame_idx: Optional frame index for naming
    
    Returns:
        List of saved file paths
    """
    global GLOBAL_TRAY_COUNTER, SAVED_TRAY_IDS
    
    # Only save when we have a current tray and it hasn't been saved yet
    if counter.current_tray_id is None or counter.current_tray_id in SAVED_TRAY_IDS:
        return []
    
    # Only save when we're in the "INSIDE" state (tray is in gate)
    if counter.state != "INSIDE" or len(trays) == 0:
        return []
    
    # Create separate directories for clean and annotated tray images
    clean_dir = Path(outdir) / "tray_images_clean"
    annotated_dir = Path(outdir) / "tray_images_annotated"
    clean_dir.mkdir(parents=True, exist_ok=True)
    annotated_dir.mkdir(parents=True, exist_ok=True)
    
    saved_files = []
    h, w = original_frame.shape[:2]
    xL, xR = band
    
    # Save only the first detected tray (usually the best detection)
    y_top, y_bot, dist, gapQ = trays[0]
    
    # Use consistent bounds for cropping
    crop_x1, crop_y1, crop_x2, crop_y2 = _tray_roi_bounds(xL, xR, y_top, y_bot, w, h, margin=20)
    
    # Crop the tray region
    tray_crop = original_frame[crop_y1:crop_y2, crop_x1:crop_x2]
    
    # Skip if crop is too small
    if tray_crop.shape[0] < 20 or tray_crop.shape[1] < 20:
        return []
    
    # Generate timestamp for filenames
    timestamp = int(time.time() * 1000)
    
    # Save 1) Clean version without any text
    clean_filename = f"tray_{GLOBAL_TRAY_COUNTER:04d}_clean_{timestamp}.png"
    clean_file_path = clean_dir / clean_filename
    success_clean = cv2.imwrite(str(clean_file_path), tray_crop)
    
    # Save 2) Annotated version with text overlay
    info_crop = tray_crop.copy()
    info_text = f"Tray #{GLOBAL_TRAY_COUNTER} | {dist}px | Gap:{gapQ:.2f}"
    
    # Add semi-transparent background for text
    overlay = info_crop.copy()
    cv2.rectangle(overlay, (5, 5), (info_crop.shape[1]-5, 35), (0, 0, 0), -1)
    cv2.addWeighted(overlay, 0.7, info_crop, 0.3, 0, info_crop)
    
    # Add text
    cv2.putText(info_crop, info_text, (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.5, 
               (0, 255, 255), 1, cv2.LINE_AA)
    
    annotated_filename = f"tray_{GLOBAL_TRAY_COUNTER:04d}_annotated_{timestamp}.png"
    annotated_file_path = annotated_dir / annotated_filename
    success_annotated = cv2.imwrite(str(annotated_file_path), info_crop)
    
    # Add both files to saved list if successful
    if success_clean:
        saved_files.append(str(clean_file_path))
    if success_annotated:
        saved_files.append(str(annotated_file_path))
        
    if success_clean or success_annotated:
        print(f"🎯 TRAY DETECTED! Saved to separate folders:")
        if success_clean:
            print(f"   📁 Clean: tray_images_clean/{clean_filename}")
        if success_annotated:
            print(f"   📁 Annotated: tray_images_annotated/{annotated_filename}")
        print(f"   📏 Size: {tray_crop.shape[1]}x{tray_crop.shape[0]}")
        
        # Mark this tray as saved
        SAVED_TRAY_IDS.add(counter.current_tray_id)
        GLOBAL_TRAY_COUNTER += 1
    else:
        print(f"❌ Failed to save tray crop versions")
    
    return saved_files

class FrameController:
    def __init__(self, target_fps=15, process_every_n=2):
        self.target_fps = target_fps
        self.process_every_n = process_every_n
        self.frame_count = 0
        self.last_process_time = time.time()
        self.min_frame_time = 1.0 / target_fps
        self.paused = False
        self.display_fps = target_fps  # For display control
        self.last_display_time = time.time()
        
    def set_fps(self, fps):
        """Manually set target FPS"""
        self.target_fps = max(1, min(fps, 60))  # Limit between 1-60 FPS
        self.display_fps = self.target_fps
        self.min_frame_time = 1.0 / self.target_fps
        print(f"FPS set to: {self.target_fps}")
        
    def increase_fps(self):
        """Increase FPS by 2"""
        self.set_fps(self.target_fps + 2)
        
    def decrease_fps(self):
        """Decrease FPS by 2"""
        self.set_fps(self.target_fps - 2)
        
    def toggle_pause(self):
        """Toggle pause state"""
        self.paused = not self.paused
        print(f"{'Paused' if self.paused else 'Resumed'}")
        if not self.paused:
            self.last_display_time = time.time()
        
    def should_process_frame(self):
        if self.paused:
            return False
            
        self.frame_count += 1
        current_time = time.time()
        
        # Skip frames based on counter
        if self.frame_count % self.process_every_n != 0:
            return False
            
        # Skip frames based on timing
        if current_time - self.last_process_time < self.min_frame_time:
            return False
            
        self.last_process_time = current_time
        return True
        
    def should_display_frame(self):
        """Control display rate independently"""
        if self.paused:
            return True  # Always allow display when paused
            
        current_time = time.time()
        display_interval = 1.0 / self.display_fps
        
        if current_time - self.last_display_time >= display_interval:
            self.last_display_time = current_time
            return True
        return False

# ------------------------ Gate (video): tray start/end ------------------------

class GateCounter:
    def __init__(self, gate_h=80, enter_frames=3, exit_frames=5):
        self.gate_h = gate_h
        self.enter_frames = enter_frames
        self.exit_frames = exit_frames
        self.seen = 0
        self.miss = 0
        self.state = "IDLE"
        self.total = 0
        self.current_tray_id = None  # Track current tray for one-time cropping

    def update(self, H, trays):
        """trays: list of (y_top, y_bot, ...) from LEN+GAP. Return events list."""
        events = []
        in_gate = any((y_bot >= H - self.gate_h) for (y_top, y_bot, *_ ) in trays)
        
        if in_gate:
            self.seen += 1
            self.miss = 0
        else:
            self.seen = 0
            self.miss += 1

        if self.state == "IDLE" and self.seen >= self.enter_frames:
            self.state = "INSIDE"
            self.current_tray_id = f"tray_{self.total + 1}"  # Assign unique ID
            events.append("TRAY_START")
        elif self.state == "INSIDE" and self.miss >= self.exit_frames:
            self.state = "IDLE"
            self.total += 1
            self.current_tray_id = None  # Clear current tray
            events.append("TRAY_END")
        
        return events

# ------------------------ Optimized Runner ------------------------

def process_frame_fast(bgr, args, counter=None, frame_idx=None, save_crops=True, color_detection=True):
    """Optimized frame processing with reduced operations, one-time tray cropping, color detection, and side panel layout."""
    mask = blue_mask_fast(bgr)
    
    # Calculate band coordinates
    xL = int((1.0 - args.band_frac) * 0.5 * bgr.shape[1])
    xR = int((1.0 + args.band_frac) * 0.5 * bgr.shape[1])
    
    # Use optimized detection
    trays, bands, runs, smooth = detect_trays_by_length_gap_fast(
        mask, xL, xR,
        len_px_range=(args.tray_min, args.tray_max),
        solid_frac=args.solid_frac,
        gap_blue_max=args.gap_blue_max
    )
    
    # Save tray crops only once per detection (when tray enters gate)
    if save_crops and len(trays) > 0 and args.save_tray_crops and counter is not None:
        save_tray_crops_once_per_detection(bgr, trays, (xL, xR), args.outdir, counter, frame_idx)
    
    # Get previous logic lines for debug modes
    prev_lines, edge = None, None
    if args.debug_mode >= 2:  # Only compute for prev logic modes
        runs2, smooth2, _ = longest_blue_runs_vectorized(mask, band_frac=args.band_frac, smooth_win=21)
        prev_lines, edge = fuse_profile_with_hough_fast(
            bgr, smooth2, xL, xR,
            width_frac_thr=0.35,  # Using default values
            hough_len_frac=0.45,
            tol=12
        )
    
    # Create appropriate overlay based on debug mode (now returns tray_color_info)
    if args.debug_mode >= 2:
        overlay, tray_color_info = draw_overlay_fast(bgr, trays, bands, (xL, xR), "prev_lines", prev_lines, edge, counter, color_detection)
    else:
        overlay, tray_color_info = draw_overlay_fast(bgr, trays, bands, (xL, xR), "len_gap", None, None, counter, color_detection)
    
    events = []
    if counter is not None:
        events = counter.update(bgr.shape[0], trays)

    # Enhanced debug view with side panel layout
    view = create_debug_view_fast(overlay, mask, args.debug_mode, tray_color_info, counter, args, color_detection)

    return view, mask, trays, events

def run_optimized(args):
    """Optimized main runner with frame skipping and color detection."""
    input_path = args.input
    is_image = False
    cap = None
    frame = None

    # Handle color detection setting
    color_detection_enabled = args.color_detection and not args.no_color_detection

    if input_path:
        img = cv2.imread(input_path)
        if img is not None:
            is_image = True
            frame = img
        else:
            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                print("ERROR: cannot open video:", input_path)
                sys.exit(1)
    else:
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("ERROR: cannot open webcam 0")
            sys.exit(1)

    outdir = Path(args.outdir)
    outdir.mkdir(parents=True, exist_ok=True)

    # Create resizable window
    window_name = "Tray Detector - Optimized with Color Detection & Side Panel"
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(window_name, 1400, 800)  # Wider to accommodate side panel

    # Create live tuning panel for video processing
    tuner_enabled = not is_image  # Only enable tuner for video/webcam
    if tuner_enabled:
        create_tuner(args)
        print("🎛️  Live tuner window created - adjust parameters in real-time!")

    if is_image:
        # Image processing (no optimization needed for single frame)
        counter = None
        view, mask, trays, events = process_frame_fast(frame, args, counter, save_crops=True, color_detection=color_detection_enabled)
        out_path = outdir / "annotated_optimized.png"
        mask_path = outdir / "mask_optimized.png"
        cv2.imwrite(str(out_path), view)
        cv2.imwrite(str(mask_path), mask)
        print("Saved:", out_path, mask_path)
        if args.save_tray_crops and len(trays) > 0:
            print(f"Saved {len(trays)} tray crops to: {outdir / 'tray_images'}")
        
        while True:
            cv2.imshow(window_name, view)
            key = cv2.waitKey(0) & 0xFF
            if key == ord('q') or key == 27:
                break
            elif key == ord('d'):
                args.debug_mode = (args.debug_mode + 1) % 5  # 0-4 modes
                view, mask, trays, events = process_frame_fast(frame, args, counter, save_crops=False, color_detection=color_detection_enabled)
                print(f"Debug mode: {args.debug_mode}")
            elif key == ord('s'):
                ts = int(time.time()*1000)
                cv2.imwrite(str(outdir / f"snapshot_{ts}.png"), view)
                print(f"Snapshot saved: snapshot_{ts}.png")
            elif key == ord('c'):  # Manual crop save
                if len(trays) > 0:
                    saved = save_tray_crops(frame, trays, 
                                          (int((1.0 - args.band_frac) * 0.5 * frame.shape[1]),
                                           int((1.0 + args.band_frac) * 0.5 * frame.shape[1])), 
                                          args.outdir)
                    print(f"Manual crop: Saved {len(saved)} tray images")
                else:
                    print("No trays detected for cropping")
            elif key == ord('x'):  # Toggle color detection
                color_detection_enabled = not color_detection_enabled
                view, mask, trays, events = process_frame_fast(frame, args, counter, save_crops=False, color_detection=color_detection_enabled)
                print(f"Color detection: {'ENABLED' if color_detection_enabled else 'DISABLED'}")
        cv2.destroyAllWindows()
        return

    # Video processing with frame control
    frame_controller = FrameController(target_fps=args.target_fps, process_every_n=args.skip_frames)
    counter = GateCounter(args.gate_h, args.enter_frames, args.exit_frames)
    
    print("OPTIMIZED VERSION WITH MANUAL CONTROLS + TRAY CROPPING + COLOR DETECTION + LIVE TUNING")
    print("=" * 80)
    print("Controls:")
    print("  'q' / ESC  : Quit")
    print("  'd'        : Toggle debug modes (0-4)")
    print("  'SPACE'    : Pause/Resume")
    print("  'i'        : Increase FPS (+2)")
    print("  'o'        : Decrease FPS (-2)")
    print("  's'        : Save current frame")
    print("  'c'        : Manual crop current trays")
    print("  't'        : Toggle auto tray cropping ON/OFF")
    print("  'x'        : Toggle color detection ON/OFF")
    print("  'r'        : Reset window size")
    if tuner_enabled:
        print("  'p'        : Print current tuned parameters")
        print("  'z'        : Reset tuner to defaults")
    print("=" * 80)
    print("Window is RESIZABLE - drag corners to resize!")
    print(f"Automatic tray cropping: {'ENABLED' if args.save_tray_crops else 'DISABLED'}")
    print(f"Color detection: {'ENABLED' if color_detection_enabled else 'DISABLED'}")
    print(f"Processing every {args.skip_frames} frames, target {args.target_fps} FPS")
    print(f"Tray crops will be saved to:")
    print(f"  📁 Clean: {outdir / 'tray_images_clean'}")
    print(f"  📁 Annotated: {outdir / 'tray_images_annotated'}")
    print("=" * 70)
    
    idx = 0
    last_view = None
    fps_counter = 0
    fps_start_time = time.time()
    
    while True:
        ok, frame = cap.read()
        if not ok:
            break
            
        idx += 1
        
        # Always try to process frames, but control display separately
        should_process = frame_controller.should_process_frame()
        should_display = frame_controller.should_display_frame()
        
        # Process frame only when needed and not paused
        if should_process:
            # Apply live tuner settings if enabled
            if tuner_enabled:
                apply_tuner(args)

            view, mask, trays, events = process_frame_fast(frame, args, counter, frame_idx=idx, save_crops=True, color_detection=color_detection_enabled)
            last_view = view
            
            for ev in events:
                print(f"[{idx}] {ev}  total={counter.total}")
        
        # Control display rate with proper FPS limiting
        if should_display and last_view is not None:
            cv2.imshow(window_name, last_view)

        # Handle key presses with proper timing
        if frame_controller.paused:
            key = cv2.waitKey(100) & 0xFF
        elif should_display:
            wait_time = max(1, int(1000 / frame_controller.target_fps))
            key = cv2.waitKey(wait_time) & 0xFF
        else:
            key = cv2.waitKey(1) & 0xFF
            
        # Handle key presses and FPS counter
        if not frame_controller.paused and should_process:
            fps_counter += 1
            if fps_counter % 30 == 0:
                elapsed = time.time() - fps_start_time
                if elapsed > 0:
                    current_fps = fps_counter / elapsed
                    print(f"Display FPS: {frame_controller.target_fps} | Processing: {current_fps:.1f} | Color: {'ON' if color_detection_enabled else 'OFF'}")
        
        # Robust key handling
        if key != 255:
            if key == ord('q') or key == 27:
                print("Exiting...")
                break
            elif key == ord('d'):
                old_mode = args.debug_mode
                args.debug_mode = (args.debug_mode + 1) % 5
                print(f"Debug mode: {old_mode} -> {args.debug_mode}")
                if last_view is not None and frame is not None:
                    view, mask, trays, events = process_frame_fast(frame, args, counter, save_crops=False, color_detection=color_detection_enabled)
                    last_view = view
            elif key == ord(' '):
                frame_controller.toggle_pause()
            elif key == ord('i'):
                frame_controller.increase_fps()
            elif key == ord('o'):
                frame_controller.decrease_fps()
            elif key == ord('s') and last_view is not None:
                ts = int(time.time()*1000)
                cv2.imwrite(str(outdir / f"frame_optimized_{ts}.png"), last_view)
                print(f"Snapshot saved: frame_optimized_{ts}.png")
            elif key == ord('c') and frame is not None:
                temp_view, temp_mask, current_trays, temp_events = process_frame_fast(frame, args, counter, save_crops=False, color_detection=color_detection_enabled)
                if len(current_trays) > 0:
                    xL = int((1.0 - args.band_frac) * 0.5 * frame.shape[1])
                    xR = int((1.0 + args.band_frac) * 0.5 * frame.shape[1])
                    saved = save_tray_crops(frame, current_trays, (xL, xR), args.outdir)
                    print(f"Manual crop: Saved {len(saved)} tray images")
                else:
                    print("No trays detected for manual cropping")
            elif key == ord('t'):
                args.save_tray_crops = not args.save_tray_crops
                status = "ENABLED" if args.save_tray_crops else "DISABLED"
                print(f"Automatic tray cropping: {status}")
            elif key == ord('x'):  # New: Toggle color detection
                color_detection_enabled = not color_detection_enabled
                print(f"Color detection: {'ENABLED' if color_detection_enabled else 'DISABLED'}")
            elif key == ord('r'):
                cv2.resizeWindow(window_name, 1400, 800)
                print("Window size reset to 1400x800")
            elif key == ord('p') and tuner_enabled:  # Print current tuned values
                print_current_values(args)
            elif key == ord('z') and tuner_enabled:  # Reset tuner to defaults
                reset_tuner_to_defaults(args)

    cap.release()
    cv2.destroyAllWindows()

# ------------------------ CLI ------------------------

def build_argparser():
    ap = argparse.ArgumentParser(description="OPTIMIZED Blue-tray detector with performance improvements and color detection")
    ap.add_argument("--input", type=str, default="./videos/2.mp4", help="image path, video path, or leave empty for webcam (default: ./videos/2.mp4)")
    ap.add_argument("--outdir", type=str, default=OUTPUT_DIR_DEFAULT, help=f"directory to save outputs (default: {OUTPUT_DIR_DEFAULT})")
    
    # Detection params using config defaults
    ap.add_argument("--band_frac", type=float, default=BAND_FRACTION_DEFAULT, help=f"fraction of width used as central band (default: {BAND_FRACTION_DEFAULT})")
    ap.add_argument("--tray_min", type=int, default=TRAY_MIN_HEIGHT_DEFAULT, help=f"expected min tray pixel height (default: {TRAY_MIN_HEIGHT_DEFAULT})")
    ap.add_argument("--tray_max", type=int, default=TRAY_MAX_HEIGHT_DEFAULT, help=f"expected max tray pixel height (default: {TRAY_MAX_HEIGHT_DEFAULT})")
    ap.add_argument("--solid_frac", type=float, default=SOLID_FRACTION_DEFAULT, help=f"solid-band width fraction (default: {SOLID_FRACTION_DEFAULT})")
    ap.add_argument("--gap_blue_max", type=float, default=GAP_BLUE_MAX_DEFAULT, help=f"max blue fraction in gap (default: {GAP_BLUE_MAX_DEFAULT})")
    
    # Video gate using config defaults
    ap.add_argument("--gate_h", type=int, default=GATE_HEIGHT_DEFAULT, help=f"bottom gate height in px (default: {GATE_HEIGHT_DEFAULT})")
    ap.add_argument("--enter_frames", type=int, default=ENTER_FRAMES_DEFAULT, help=f"frames to confirm TRAY_START (default: {ENTER_FRAMES_DEFAULT})")
    ap.add_argument("--exit_frames", type=int, default=EXIT_FRAMES_DEFAULT, help=f"frames to confirm TRAY_END (default: {EXIT_FRAMES_DEFAULT})")
    
    # Performance optimization parameters using config defaults
    ap.add_argument("--target_fps", type=int, default=TARGET_FPS_DEFAULT, help=f"target processing FPS (default: {TARGET_FPS_DEFAULT})")
    ap.add_argument("--skip_frames", type=int, default=SKIP_FRAMES_DEFAULT, help=f"process every Nth frame (default: {SKIP_FRAMES_DEFAULT})")
    
    # Tray cropping parameters
    ap.add_argument("--save_tray_crops", action="store_true", help="automatically save cropped tray images (one per detection)")
    ap.add_argument("--detailed_crops", action="store_true", help="save crops with info overlay (slower)")
    
    # Color detection parameters
    ap.add_argument("--color_detection", action="store_true", default=True, help="enable pouch color detection (default: enabled)")
    ap.add_argument("--no_color_detection", action="store_true", help="disable pouch color detection for maximum speed")
    
    # Simplified debug mode
    ap.add_argument("--debug_mode", type=int, default=0, help="0:LEN+GAP, 1:LEN+GAP+Mask, 2:Prev, 3:Prev+Mask, 4:Split")
    
    return ap

if __name__ == "__main__":
    args = build_argparser().parse_args()
    run_optimized(args)
