#!/usr/bin/env python3
import os, sys, cv2, time, argparse, numpy as np
from pathlib import Path

# --- blue mask (tight) ---
def blue_mask(bgr):
    hsv = cv2.cvtColor(bgr, cv2.COLOR_BGR2HSV)
    mask = cv2.inRange(hsv, (96,60,40), (135,255,255))
    mask = cv2.medianBlur(mask, 5)
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, cv2.getStructuringElement(cv2.MORPH_RECT,(5,5)), 1)
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, cv2.getStructuringElement(cv2.MORPH_RECT,(7,7)), 2)
    return mask

# --- longest blue run per-row inside central band ---
def longest_run_signal(mask, band_frac=0.62, smooth_win=23):
    h,w = mask.shape
    xL = int((1.0-band_frac)*0.5*w); xR = int((1.0+band_frac)*0.5*w)
    core = (mask[:,xL:xR]>0).astype(np.uint8)
    runs = np.zeros(h, np.int32)
    for y in range(h):
        row = core[y]; 
        if row.sum()==0: continue
        padded = np.pad(row,(1,1),'constant')
        diffs = np.diff(padded.astype(np.int16))
        s = np.where(diffs==1)[0]; e = np.where(diffs==-1)[0]
        if s.size and e.size:
            runs[y] = int((e - s).max())
    k = np.ones(smooth_win)/smooth_win
    smooth = np.convolve(runs.astype(np.float32), k, mode="same")
    return runs, smooth, (xL,xR)

def find_bands_from_signal(smooth, band_w, min_frac=0.40, cluster_gap=10):
    thr = band_w*min_frac
    idx = np.where(smooth>=thr)[0]
    bands=[]
    if idx.size:
        parts = np.split(idx, np.where(np.diff(idx)>cluster_gap)[0]+1)
        for g in parts:
            bands.append((int(np.mean(g)), g[0], g[-1], float(smooth[g].max()/band_w)))  # (yc, y0, y1, qual)
    return bands

def choose_tray_pair(bands, core_mask, rng=(300,430), target=365):
    trays=[]
    H,W = core_mask.shape
    for i in range(len(bands)):
        for j in range(i+1,len(bands)):
            y1, y1s, y1e, q1 = bands[i]
            y2, y2s, y2e, q2 = bands[j]
            d = abs(y2-y1)
            if d<rng[0] or d>rng[1]: continue
            top,bottom = min(y1,y2), max(y1,y2)
            gS,gE = min(y1e,y2e), max(y1s,y2s)
            if gS>=gE: gap_blue = 0.0
            else:
                gap = core_mask[gS:gE,:]
                gap_blue = 0.0 if gap.size==0 else float((gap.mean(axis=1) >= 0.30).mean())
            if gap_blue>0.35:  # gap must be non-blue
                continue
            length_score = 1.0 - min(1.0, abs(d-target)/target)
            score = 0.55*length_score + 0.25*(1.0-gap_blue) + 0.20*((q1+q2)/2.0)
            trays.append((top,bottom,d,gap_blue,score))
    if not trays: return None
    trays.sort(key=lambda t:-t[4])
    return trays[0]

def find_divider(smooth, band_w, y_top, y_bot):
    if y_bot-y_top < 80: return None
    seg = smooth[y_top+25:y_bot-25]
    if seg.size==0: return None
    thr = band_w*0.35
    idx = np.where(seg>=thr)[0]
    if not idx.size: return None
    parts = np.split(idx, np.where(np.diff(idx)>10)[0]+1)
    peaks = [int(np.mean(p))+y_top+25 for p in parts]
    mid = (y_top+y_bot)//2
    peaks.sort(key=lambda y: abs(y-mid))
    return peaks[0]

# --- gate for video ---
class Gate:
    def __init__(self, h=80, enter=3, exit=5):
        self.h=h; self.enter=enter; self.exit=exit
        self.seen=0; self.miss=0; self.state="IDLE"; self.total=0
    def update(self, H, trays):
        events=[]
        in_gate = any((t[1]>=H-self.h) for t in trays) if trays else False
        if in_gate: self.seen+=1; self.miss=0
        else: self.seen=0; self.miss+=1
        if self.state=="IDLE" and self.seen>=self.enter:
            self.state="INSIDE"; events.append("TRAY_START")
        elif self.state=="INSIDE" and self.miss>=self.exit:
            self.state="IDLE"; self.total+=1; events.append("TRAY_END")
        return events

def draw_overlay(bgr, mask, bands, tray, divider, band):
    out=bgr.copy(); H,W=out.shape[:2]; xL,xR=band
    cv2.rectangle(out,(xL,0),(xR,H-1),(255,255,255),1)
    for yc,y0,y1,q in bands:
        cv2.line(out,(0,y0),(W-1,y0),(255,120,0),1)
        cv2.line(out,(0,y1),(W-1,y1),(255,120,0),1)
    if tray:
        y_top,y_bot,d,gap,score=tray
        cv2.line(out,(0,y_top),(W-1,y_top),(36,255,12),2)
        cv2.line(out,(0,y_bot),(W-1,y_bot),(36,255,12),2)
        cv2.putText(out,f"d={d}px gapQ={gap:.02f} score={score:.02f}",(10,max(24,y_top-8)),
                    cv2.FONT_HERSHEY_SIMPLEX,0.6,(0,0,0),2,cv2.LINE_AA)
        cv2.putText(out,f"d={d}px gapQ={gap:.02f} score={score:.02f}",(10,max(24,y_top-8)),
                    cv2.FONT_HERSHEY_SIMPLEX,0.6,(36,255,12),1,cv2.LINE_AA)
    else:
        cv2.putText(out,"NO FULL TRAY PAIR",(10,30),cv2.FONT_HERSHEY_SIMPLEX,0.8,(0,0,0),2,cv2.LINE_AA)
        cv2.putText(out,"NO FULL TRAY PAIR",(10,30),cv2.FONT_HERSHEY_SIMPLEX,0.8,(0,165,255),2,cv2.LINE_AA)
    if divider:
        cv2.line(out,(0,divider),(W-1,divider),(0,255,255),2)
        cv2.putText(out,f"divider~{divider}",(10,divider-6),cv2.FONT_HERSHEY_SIMPLEX,0.6,(0,0,0),2,cv2.LINE_AA)
        cv2.putText(out,f"divider~{divider}",(10,divider-6),cv2.FONT_HERSHEY_SIMPLEX,0.6,(0,255,255),1,cv2.LINE_AA)
    side = np.hstack([out, cv2.cvtColor(cv2.resize(mask,(W,H)), cv2.COLOR_GRAY2BGR)])
    return out, side

def process_frame(bgr, args, counter=None):
    mask = blue_mask(bgr)
    runs, smooth, (xL,xR) = longest_run_signal(mask, args.band_frac, 23)
    band_w = (xR-xL)
    core = (mask[:,xL:xR]>0).astype(np.uint8)
    bands = find_bands_from_signal(smooth, band_w, min_frac=0.40, cluster_gap=10)
    tray  = choose_tray_pair(bands, core, rng=(args.tray_min,args.tray_max), target=(args.tray_min+args.tray_max)//2)
    divider = find_divider(smooth, band_w, tray[0], tray[1]) if tray else None
    overlay, debug = draw_overlay(bgr, mask, bands, tray, divider, (xL,xR))
    events=[]
    if counter is not None and tray:
        events = counter.update(bgr.shape[0], [tray])
    return overlay, debug, mask, tray, divider, events

def run(args):
    outdir = Path(args.outdir); outdir.mkdir(parents=True, exist_ok=True)
    # image or video?
    img = cv2.imread(args.input) if args.input else None
    if img is not None:
        counter=None
        overlay, debug, mask, tray, divider, _ = process_frame(img, args, counter)
        cv2.imwrite(str(outdir/"annotated.png"), overlay)
        cv2.imwrite(str(outdir/"mask.png"), mask)
        print("Saved:", outdir/"annotated.png", outdir/"mask.png")
        view = debug if args.debug_mode else overlay
        while True:
            cv2.imshow("tray-len-gap", view)
            k=cv2.waitKey(0)&0xFF
            if k in (27, ord('q')): break
            elif k==ord('d'):
                args.debug_mode = 1-args.debug_mode
                view = debug if args.debug_mode else overlay
            elif k==ord('s'):
                ts=int(time.time()*1000); cv2.imwrite(str(outdir/f"snapshot_{ts}.png"), view)
                print("snapshot saved")
        cv2.destroyAllWindows(); return
    # video
    cap = cv2.VideoCapture(0 if not args.input else args.input)
    if not cap.isOpened(): print("ERROR opening video/webcam"); sys.exit(1)
    writer=None
    if args.write_video:
        fourcc = cv2.VideoWriter_fourcc(*"mp4v")
        W=int(cap.get(3)*2); H=int(cap.get(4))
        writer = cv2.VideoWriter(str(outdir/"annotated.mp4"), fourcc, cap.get(5) or 30.0, (W,H))
    gate = Gate(args.gate_h,args.enter_frames,args.exit_frames)
    print("Press d(toggle), s(save), q(quit)")
    while True:
        ok,frame = cap.read()
        if not ok: break
        overlay, debug, mask, tray, divider, events = process_frame(frame, args, gate)
        view = debug if args.debug_mode else overlay
        # HUD
        cv2.rectangle(view, (0, view.shape[0]-args.gate_h), (view.shape[1]-1, view.shape[0]-1), (255,255,255), 2)
        cv2.putText(view, f"gate:{args.gate_h}px state:{gate.state} total:{gate.total}",
                    (10,46), cv2.FONT_HERSHEY_SIMPLEX,0.6,(0,0,0),2,cv2.LINE_AA)
        cv2.putText(view, f"gate:{args.gate_h}px state:{gate.state} total:{gate.total}",
                    (10,46), cv2.FONT_HERSHEY_SIMPLEX,0.6,(0,255,255),1,cv2.LINE_AA)
        for ev in events: print(ev, "total=", gate.total)
        cv2.imshow("tray-len-gap", view)
        if writer is not None:
            writer.write(cv2.resize(view, (int(cap.get(3)*2), int(cap.get(4)))))
        k=cv2.waitKey(1)&0xFF
        if k in (27, ord('q')): break
        elif k==ord('d'): args.debug_mode = 1-args.debug_mode
        elif k==ord('s'):
            ts=int(time.time()*1000)
            cv2.imwrite(str(outdir/f"frame_{ts}.png"), view); cv2.imwrite(str(outdir/f"mask_{ts}.png"), mask)
            print("snapshot saved")
    cap.release(); 
    if writer is not None: writer.release()
    cv2.destroyAllWindows()

if __name__=="__main__":
    ap = argparse.ArgumentParser(description="Simple robust LEN+GAP tray detector (images & videos).")
    ap.add_argument("--input", type=str, default="", help="image path or video path (empty for webcam)")
    ap.add_argument("--outdir", type=str, default="out", help="save dir")
    ap.add_argument("--band_frac", type=float, default=0.62, help="central width fraction")
    ap.add_argument("--tray_min", type=int, default=300, help="min tray height px")
    ap.add_argument("--tray_max", type=int, default=430, help="max tray height px")
    ap.add_argument("--gate_h", type=int, default=80, help="bottom gate size for video")
    ap.add_argument("--enter_frames", type=int, default=3)
    ap.add_argument("--exit_frames", type=int, default=5)
    ap.add_argument("--write_video", action="store_true")
    ap.add_argument("--debug_mode", type=int, default=1, help="1=overlay+mask, 0=overlay only")
    args = ap.parse_args()
    run(args)
